package main

import (
	"fmt"
	"os"
	"strings"

	"github.com/yourusername/epubcraft/internal/reader"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run quick_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB reader with file: %s\n", epubFile)

	// Test HTML to text conversion with a simple example
	htmlContent := `<html><body><h1>Test Chapter</h1><p>This is a test paragraph with some <strong>bold</strong> text.</p><p>Another paragraph here.</p></body></html>`
	
	// Test our HTML converter directly
	fmt.Println("Testing HTML to text conversion...")
	text := reader.TestHtmlToText(htmlContent)
	fmt.Printf("Converted text:\n%s\n", text)
	
	if len(strings.TrimSpace(text)) > 0 {
		fmt.Println("✓ HTML to text conversion is working!")
	} else {
		fmt.Println("✗ HTML to text conversion failed!")
		os.Exit(1)
	}

	fmt.Println("Basic functionality test completed successfully!")
}
