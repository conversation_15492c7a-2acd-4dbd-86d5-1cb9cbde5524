package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/taylorskalyo/goreader/epub"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run timeout_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB file with 5-second timeout: %s\n", epubFile)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Channel to receive result
	done := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				done <- fmt.Errorf("panic: %v", r)
			}
		}()

		fmt.Println("Opening EPUB file...")
		rc, err := epub.OpenReader(epubFile)
		if err != nil {
			done <- fmt.Errorf("error opening EPUB file: %v", err)
			return
		}
		defer rc.Close()

		fmt.Println("✓ EPUB opened successfully")
		
		if len(rc.Rootfiles) == 0 {
			done <- fmt.Errorf("no rootfiles found")
			return
		}

		pkg := &rc.Rootfiles[0].Package
		fmt.Printf("✓ Found %d spine items\n", len(pkg.Spine.Itemrefs))

		done <- nil
	}()

	select {
	case err := <-done:
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("✓ Test completed successfully")
	case <-ctx.Done():
		fmt.Println("✗ Test timed out after 5 seconds")
		fmt.Println("This suggests the EPUB library is hanging during file opening")
		os.Exit(1)
	}
}
