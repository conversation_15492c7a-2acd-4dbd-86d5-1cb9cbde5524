package converter

import (
	"fmt"
	"os"
	"strings"

	"github.com/bmaupin/go-epub"
	"github.com/unidoc/unipdf/v3/common/license"
	"github.com/unidoc/unipdf/v3/extractor"
	pdf "github.com/unidoc/unipdf/v3/model"
)

// Initialize the unidoc license (required for unipdf)
func init() {
	// This is a trial key - for production use, replace with a valid license key
	err := license.SetMeteredKey(os.Getenv("UNIDOC_LICENSE_KEY"))
	if err != nil {
		fmt.Printf("Warning: Failed to set Unidoc license key: %v\n", err)
	}
}

// ConvertPDFToEPUB converts a PDF file to EPUB format
func ConvertPDFToEPUB(pdfPath, epubPath, title, author string, progressCallback func(int, int)) error {
	// Create a new EPUB
	e := epub.NewEpub(title)
	if author != "" {
		e.<PERSON>(author)
	}

	// Open the PDF file
	f, err := os.Open(pdfPath)
	if err != nil {
		return fmt.Errorf("error opening PDF file: %v", err)
	}
	defer f.Close()

	// Create PDF reader
	pdfReader, err := pdf.NewPdfReader(f)
	if err != nil {
		return fmt.Errorf("error creating PDF reader: %v", err)
	}

	// Get the number of pages in the PDF
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("error getting number of pages: %v", err)
	}

	// Notify total pages
	if progressCallback != nil {
		progressCallback(0, numPages)
	}

	// Process each page
	for i := 1; i <= numPages; i++ {
		if progressCallback != nil {
			progressCallback(i, numPages)
		}

		pageNum := i
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			return fmt.Errorf("error loading page %d: %v", pageNum, err)
		}

		// Extract text
		ex, err := extractor.New(page)
		if err != nil {
			return fmt.Errorf("error creating extractor: %v", err)
		}

		text, err := ex.ExtractText()
		if err != nil {
			return fmt.Errorf("error extracting text from page %d: %v", pageNum, err)
		}

		// Create HTML content for the page
		htmlContent := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Page %d</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 2em;
        }
        .page-break {
            page-break-after: always;
        }
        img {
            max-width: 100%%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="content">
        %s
    </div>
    <div class="page-break"></div>
</body>
</html>`, pageNum, strings.ReplaceAll(text, "\n", "<br>"))

		// Add the page to the EPUB
		sectionTitle := fmt.Sprintf("Page %d", pageNum)
		sectionPath := fmt.Sprintf("page_%d.html", pageNum)
		if _, err := e.AddSection(htmlContent, sectionTitle, sectionPath, ""); err != nil {
			return fmt.Errorf("error adding page %d to EPUB: %v", pageNum, err)
		}

		// Extract and add images (if any)
		// Note: Image extraction is more complex and may require additional processing
		// For now, we'll focus on text extraction
	}

	// Set the cover page (first page as cover)
	if numPages > 0 {
		e.SetCover("page_1.html", "")
	}

	// Create the EPUB file
	if err := e.Write(epubPath); err != nil {
		return fmt.Errorf("error writing EPUB file: %v", err)
	}

	return nil
}
