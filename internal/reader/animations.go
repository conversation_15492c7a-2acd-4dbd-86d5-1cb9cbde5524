package reader

import (
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/fatih/color"
	"golang.org/x/term"
)

// AnimationType represents different types of animations
type AnimationType int

const (
	AnimationNone AnimationType = iota
	AnimationSlideLeft
	AnimationSlideRight
	AnimationFade
	AnimationTypewriter
	AnimationWipe
	AnimationZoom
)

// AnimationConfig holds configuration for animations
type AnimationConfig struct {
	Type     AnimationType
	Duration time.Duration
	Enabled  bool
}

// AnimationManager handles all animations in the EPUB reader
type AnimationManager struct {
	config AnimationConfig
	width  int
	height int
}

// NewAnimationManager creates a new animation manager
func NewAnimationManager() *AnimationManager {
	return &AnimationManager{
		config: AnimationConfig{
			Type:     AnimationSlideLeft,
			Duration: 300 * time.Millisecond,
			Enabled:  true,
		},
	}
}

// SetAnimationType sets the animation type
func (am *AnimationManager) SetAnimationType(animType AnimationType) {
	am.config.Type = animType
}

// SetDuration sets the animation duration
func (am *AnimationManager) SetDuration(duration time.Duration) {
	am.config.Duration = duration
}

// SetEnabled enables or disables animations
func (am *AnimationManager) SetEnabled(enabled bool) {
	am.config.Enabled = enabled
}

// UpdateTerminalSize updates the terminal dimensions
func (am *AnimationManager) UpdateTerminalSize(w io.Writer) {
	if f, ok := w.(*os.File); ok && term.IsTerminal(int(f.Fd())) {
		width, height, err := term.GetSize(int(f.Fd()))
		if err == nil {
			am.width = width
			am.height = height
		}
	}
	if am.width == 0 {
		am.width = 80
	}
	if am.height == 0 {
		am.height = 24
	}
}

// AnimatePageTransition performs a page transition animation
func (am *AnimationManager) AnimatePageTransition(w io.Writer, oldContent, newContent []string, direction int) {
	if !am.config.Enabled {
		am.renderContent(w, newContent)
		return
	}

	am.UpdateTerminalSize(w)

	switch am.config.Type {
	case AnimationSlideLeft:
		am.animateSlide(w, oldContent, newContent, direction, true)
	case AnimationSlideRight:
		am.animateSlide(w, oldContent, newContent, direction, false)
	case AnimationFade:
		am.animateFade(w, oldContent, newContent)
	case AnimationTypewriter:
		am.animateTypewriter(w, newContent)
	case AnimationWipe:
		am.animateWipe(w, oldContent, newContent, direction)
	case AnimationZoom:
		am.animateZoom(w, newContent)
	default:
		am.renderContent(w, newContent)
	}
}

// animateSlide creates a sliding animation between pages
func (am *AnimationManager) animateSlide(w io.Writer, oldContent, newContent []string, direction int, leftSlide bool) {
	steps := 20
	stepDuration := am.config.Duration / time.Duration(steps)

	for step := 0; step <= steps; step++ {
		am.clearScreen(w)

		progress := float64(step) / float64(steps)

		// Calculate positions
		var oldPos, newPos int
		if leftSlide {
			if direction > 0 { // Next page
				oldPos = int(-progress * float64(am.width))
				newPos = int(float64(am.width) * (1 - progress))
			} else { // Previous page
				oldPos = int(progress * float64(am.width))
				newPos = int(-float64(am.width) * (1 - progress))
			}
		} else {
			if direction > 0 { // Next page
				oldPos = int(progress * float64(am.width))
				newPos = int(-float64(am.width) * (1 - progress))
			} else { // Previous page
				oldPos = int(-progress * float64(am.width))
				newPos = int(float64(am.width) * (1 - progress))
			}
		}

		// Render old content at oldPos
		if step < steps {
			am.renderContentAtPosition(w, oldContent, oldPos)
		}

		// Render new content at newPos
		am.renderContentAtPosition(w, newContent, newPos)

		if step < steps {
			time.Sleep(stepDuration)
		}
	}
}

// animateFade creates a fade transition between pages
func (am *AnimationManager) animateFade(w io.Writer, oldContent, newContent []string) {
	steps := 10
	stepDuration := am.config.Duration / time.Duration(steps)

	// Fade out old content
	for step := 0; step < steps; step++ {
		am.clearScreen(w)
		opacity := 1.0 - (float64(step) / float64(steps))
		am.renderContentWithOpacity(w, oldContent, opacity)
		time.Sleep(stepDuration)
	}

	// Fade in new content
	for step := 0; step <= steps; step++ {
		am.clearScreen(w)
		opacity := float64(step) / float64(steps)
		am.renderContentWithOpacity(w, newContent, opacity)
		if step < steps {
			time.Sleep(stepDuration)
		}
	}
}

// animateTypewriter creates a typewriter effect for new content
func (am *AnimationManager) animateTypewriter(w io.Writer, content []string) {
	am.clearScreen(w)

	totalChars := 0
	for _, line := range content {
		totalChars += len(line) + 1 // +1 for newline
	}

	if totalChars == 0 {
		return
	}

	charDelay := am.config.Duration / time.Duration(totalChars)
	if charDelay < time.Millisecond {
		charDelay = time.Millisecond
	}

	currentLine := 0
	currentChar := 0

	for currentLine < len(content) {
		am.clearScreen(w)

		// Render completed lines
		for i := 0; i < currentLine; i++ {
			fmt.Fprintln(w, content[i])
		}

		// Render current line up to currentChar
		if currentLine < len(content) {
			line := content[currentLine]
			if currentChar <= len(line) {
				fmt.Fprint(w, line[:currentChar])
				if currentChar < len(line) {
					// Add blinking cursor
					fmt.Fprint(w, "█")
				}
			}
		}

		time.Sleep(charDelay)

		currentChar++
		if currentLine < len(content) && currentChar > len(content[currentLine]) {
			currentLine++
			currentChar = 0
		}
	}

	// Final render without cursor
	am.clearScreen(w)
	am.renderContent(w, content)
}

// animateWipe creates a wipe transition
func (am *AnimationManager) animateWipe(w io.Writer, oldContent, newContent []string, direction int) {
	steps := 15
	stepDuration := am.config.Duration / time.Duration(steps)

	for step := 0; step <= steps; step++ {
		am.clearScreen(w)

		progress := float64(step) / float64(steps)
		wipePos := int(progress * float64(am.height))

		// Render content with wipe effect
		for i, line := range newContent {
			if i < len(newContent) {
				if direction > 0 { // Top to bottom wipe
					if i <= wipePos {
						fmt.Fprintln(w, line)
					} else if i < len(oldContent) {
						fmt.Fprintln(w, oldContent[i])
					} else {
						fmt.Fprintln(w)
					}
				} else { // Bottom to top wipe
					if i >= len(newContent)-wipePos {
						fmt.Fprintln(w, line)
					} else if i < len(oldContent) {
						fmt.Fprintln(w, oldContent[i])
					} else {
						fmt.Fprintln(w)
					}
				}
			}
		}

		if step < steps {
			time.Sleep(stepDuration)
		}
	}
}

// animateZoom creates a zoom-in effect
func (am *AnimationManager) animateZoom(w io.Writer, content []string) {
	steps := 12
	stepDuration := am.config.Duration / time.Duration(steps)

	for step := 0; step <= steps; step++ {
		am.clearScreen(w)

		progress := float64(step) / float64(steps)
		scale := 0.3 + (0.7 * progress) // Scale from 30% to 100%

		// Calculate content dimensions after scaling
		scaledHeight := int(float64(len(content)) * scale)
		scaledWidth := int(float64(am.width) * scale)

		// Center the scaled content
		offsetY := (am.height - scaledHeight) / 2
		offsetX := (am.width - scaledWidth) / 2

		// Render scaled content
		for i := 0; i < offsetY && i < am.height; i++ {
			fmt.Fprintln(w)
		}

		for i, line := range content {
			if i >= scaledHeight {
				break
			}

			// Scale the line
			scaledLine := line
			if len(line) > scaledWidth {
				scaledLine = line[:scaledWidth]
			}

			// Add horizontal offset
			padding := strings.Repeat(" ", offsetX)
			fmt.Fprintf(w, "%s%s\n", padding, scaledLine)
		}

		if step < steps {
			time.Sleep(stepDuration)
		}
	}
}

// Helper functions

func (am *AnimationManager) clearScreen(w io.Writer) {
	fmt.Fprint(w, "\033[H\033[2J")
}

func (am *AnimationManager) renderContent(w io.Writer, content []string) {
	for _, line := range content {
		fmt.Fprintln(w, line)
	}
}

func (am *AnimationManager) renderContentAtPosition(w io.Writer, content []string, xOffset int) {
	for i, line := range content {
		if i >= am.height-1 { // Leave room for status
			break
		}

		// Move cursor to position
		fmt.Fprintf(w, "\033[%d;%dH", i+1, max(1, xOffset+1))

		// Only render if position is visible
		if xOffset < am.width && xOffset > -len(line) {
			visibleStart := max(0, -xOffset)
			visibleEnd := min(len(line), am.width-xOffset)

			if visibleStart < visibleEnd {
				fmt.Fprint(w, line[visibleStart:visibleEnd])
			}
		}
	}
}

func (am *AnimationManager) renderContentWithOpacity(w io.Writer, content []string, opacity float64) {
	// Simulate opacity by using different colors/brightness
	var colorFunc func(string, ...interface{}) string

	if opacity > 0.8 {
		colorFunc = color.New(color.FgWhite).SprintfFunc()
	} else if opacity > 0.6 {
		colorFunc = color.New(color.FgHiBlack).SprintfFunc()
	} else if opacity > 0.4 {
		colorFunc = color.New(color.FgBlack).SprintfFunc()
	} else if opacity > 0.2 {
		colorFunc = color.New(color.FgBlack, color.Faint).SprintfFunc()
	} else {
		colorFunc = func(format string, args ...interface{}) string { return "" }
	}

	for _, line := range content {
		fmt.Fprintln(w, colorFunc("%s", line))
	}
}

// Utility functions
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
