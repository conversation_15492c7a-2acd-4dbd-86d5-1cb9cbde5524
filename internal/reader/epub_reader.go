package reader

import (
	"fmt"
	"io"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/briandowns/spinner"
	"github.com/fatih/color"
	"github.com/mattn/go-runewidth"
	"github.com/taylorskalyo/goreader/epub"
	"golang.org/x/term"
)

// EPUBReader represents an EPUB document reader
type EPUBReader struct {
	rc       *epub.ReadCloser
	pkg      *epub.Package
	pages    []string
	pageNum  int
	pageSize int
	font     string
	align    Alignment
	layout   LayoutMode
	mu       sync.Mutex // Mutex to protect concurrent access to pageNum

	// New features
	stateManager      *StateManager
	navigationHandler *NavigationHandler
	filePath          string
}

// showSpinner wraps an operation with a loading spinner.
func showSpinner(message string, operation func()) {
	s := spinner.New(spinner.CharSets[14], 100*time.Millisecond)
	s.Prefix = message + " "
	s.Color("cyan")
	s.Start()
	operation() // Execute the actual work
	s.Stop()
}

// SetFont sets the font for rendering text
func (r *EPUBReader) SetFont(fontName string) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.font = fontName
}

// Align returns the current text alignment
func (r *EPUBReader) Align() Alignment {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.align
}

// SetAlign sets the text alignment
func (r *EPUBReader) SetAlign(align Alignment) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.align = align
}

// Layout returns the current layout mode
func (r *EPUBReader) Layout() LayoutMode {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.layout
}

// SetLayout sets the layout mode
func (r *EPUBReader) SetLayout(layout LayoutMode) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.layout = layout
}

// NewEPUBReader creates a new EPUB reader from a file with the specified font, alignment, and layout
func NewEPUBReader(filePath, font string, align Alignment, layout LayoutMode) (*EPUBReader, error) {
	fmt.Fprintf(os.Stderr, "[DEBUG] Starting NewEPUBReader for file: %s\n", filePath)

	// Initialize state manager
	stateManager := NewStateManager()

	// Try to load saved state
	savedState, err := stateManager.LoadState(filePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] Failed to load state: %v\n", err)
	} else if savedState != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] Loaded saved state: page %d, font %s, align %s, layout %s\n",
			savedState.PageNum, savedState.Font, savedState.Alignment, savedState.Layout)
		// Use saved settings if available
		if savedState.Font != "" {
			font = savedState.Font
		}
		if savedState.Alignment != "" {
			align = stringToAlignment(savedState.Alignment)
		}
		if savedState.Layout != "" {
			layout = stringToLayout(savedState.Layout)
		}
	}

	// Open the EPUB file
	rc, err := epub.OpenReader(filePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] Failed to open EPUB file: %v\n", err)
		return nil, fmt.Errorf("error opening EPUB file: %v", err)
	}
	fmt.Fprintf(os.Stderr, "[DEBUG] Successfully opened EPUB file\n")

	// Get the OPF file (package document)
	if len(rc.Rootfiles) == 0 {
		fmt.Fprintf(os.Stderr, "[DEBUG] No rootfiles found in EPUB\n")
		rc.Close()
		return nil, fmt.Errorf("no rootfile found in EPUB")
	}
	fmt.Fprintf(os.Stderr, "[DEBUG] Found %d rootfiles\n", len(rc.Rootfiles))

	// Use the first rootfile (most EPUBs only have one)
	pkg := &rc.Rootfiles[0].Package
	fmt.Fprintf(os.Stderr, "[DEBUG] Spine has %d itemrefs\n", len(pkg.Spine.Itemrefs))
	fmt.Fprintf(os.Stderr, "[DEBUG] Manifest has %d items\n", len(pkg.Manifest.Items))

	// Get the spine items (main content in reading order)
	items := make([]epub.Item, 0, len(pkg.Spine.Itemrefs))

	// Find all items in the spine
	for _, itemref := range pkg.Spine.Itemrefs {
		fmt.Fprintf(os.Stderr, "[DEBUG] Looking for spine item with ID: %s\n", itemref.IDREF)
		// Find the item in the manifest
		for _, item := range pkg.Manifest.Items {
			if item.ID == itemref.IDREF {
				fmt.Fprintf(os.Stderr, "[DEBUG] Found spine item: ID=%s, MediaType=%s, HREF=%s\n", item.ID, item.MediaType, item.HREF)
				items = append(items, item)
				break
			}
		}
	}

	// DEBUG: Print number of spine items
	fmt.Fprintf(os.Stderr, "[DEBUG] Number of spine items: %d\n", len(items))

	// Extract text content from each spine item
	pages := make([]string, 0, len(items))
	for idx, item := range items {
		fmt.Fprintf(os.Stderr, "[DEBUG] Processing item %d/%d: ID=%s, MediaType=%s\n", idx+1, len(items), item.ID, item.MediaType)

		// Get the content of the item
		r, err := item.Open()
		if err != nil {
			fmt.Fprintf(os.Stderr, "[DEBUG] Failed to open item %d (ID: %s, MediaType: %s): %v\n", idx, item.ID, item.MediaType, err)
			continue // Skip items with errors
		}

		content, err := io.ReadAll(r)
		r.Close()
		if err != nil {
			fmt.Fprintf(os.Stderr, "[DEBUG] Failed to read item %d (ID: %s, MediaType: %s): %v\n", idx, item.ID, item.MediaType, err)
			continue // Skip items with read errors
		}

		// DEBUG: Print length of HTML content
		fmt.Fprintf(os.Stderr, "[DEBUG] Item %d (ID: %s, MediaType: %s): HTML length = %d\n", idx, item.ID, item.MediaType, len(content))

		// Simple HTML to text conversion
		// NOTE: htmlToText is an external helper function not included in this file.
		// Its correct implementation is crucial for extracting readable content, including handling potential fallbacks.
		text := htmlToText(string(content))

		// DEBUG: Print length of resulting text
		fmt.Fprintf(os.Stderr, "[DEBUG] Item %d (ID: %s): Extracted text length = %d\n", idx, item.ID, len(text))

		// Always append the extracted text. The htmlToText function is expected to return the best possible text.
		pages = append(pages, text)
		fmt.Fprintf(os.Stderr, "[DEBUG] Added page %d to pages array\n", len(pages))
	}

	fmt.Fprintf(os.Stderr, "[DEBUG] Total pages extracted: %d\n", len(pages))

	// Check if any pages were extracted. If not, it indicates a problem with the EPUB or the htmlToText function.
	if len(pages) == 0 {
		rc.Close()
		return nil, fmt.Errorf("could not extract any readable content from EPUB file: %s. This might be due to a malformed EPUB or an issue with the HTML to text conversion.", filePath)
	}

	// Get terminal size for pagination
	_, height, err := term.GetSize(int(os.Stdout.Fd()))
	if err != nil {
		height = 24 // Default to 24 lines if we can't get terminal size
	}

	// Determine starting page number
	startPage := 0
	if savedState != nil && savedState.PageNum > 0 && savedState.PageNum <= len(pages) {
		startPage = savedState.PageNum - 1 // Convert to 0-based
		fmt.Fprintf(os.Stderr, "[DEBUG] Starting at saved page: %d\n", savedState.PageNum)
	}

	reader := &EPUBReader{
		rc:       rc,
		pkg:      pkg,
		pages:    pages,
		pageNum:  startPage,
		pageSize: height - 4, // Leave room for status line
		font:     font,
		align:    align,
		layout:   layout,

		// New features
		stateManager:      stateManager,
		navigationHandler: NewNavigationHandler(nil), // Will be set after reader creation
		filePath:          filePath,
	}

	// Set up navigation handler with the reader
	reader.navigationHandler = NewNavigationHandler(reader)

	fmt.Fprintf(os.Stderr, "[DEBUG] EPUBReader created successfully with %d pages\n", len(pages))
	return reader, nil
}

// CurrentPage returns the current page content
func (r *EPUBReader) CurrentPage() string {
	if len(r.pages) == 0 {
		return "No content available"
	}
	if r.pageNum < 0 || r.pageNum >= len(r.pages) {
		return "Page out of range"
	}
	return r.pages[r.pageNum]
}

// NextPage moves to the next page with a spinner animation
func (r *EPUBReader) NextPage() bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.pageNum >= len(r.pages)-1 {
		return false
	}

	showSpinner("Loading next page", func() {
		r.pageNum++
	})
	return true
}

// PrevPage moves to the previous page with a spinner animation
func (r *EPUBReader) PrevPage() bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.pageNum <= 0 {
		return false
	}

	showSpinner("Loading previous page", func() {
		r.pageNum--
	})
	return true
}

// GotoPage jumps to a specific page (0-based) with a spinner animation
func (r *EPUBReader) GotoPage(pageNum int) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if pageNum < 0 || pageNum >= len(r.pages) {
		return false
	}

	showSpinner("Loading page", func() {
		r.pageNum = pageNum
	})
	return true
}

// PageInfo returns the current page number and total pages
func (r *EPUBReader) PageInfo() (int, int) {
	return r.pageNum + 1, len(r.pages)
}

// SaveState saves the current reading state
func (r *EPUBReader) SaveState() error {
	return r.stateManager.SaveState(r.filePath, r.pageNum+1, r.font, r.align, r.layout)
}

// Close closes the EPUB reader and saves state
func (r *EPUBReader) Close() error {
	// Save state before closing
	if err := r.SaveState(); err != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] Failed to save state: %v\n", err)
	}

	// Close the EPUB file
	if r.rc != nil {
		r.rc.Close()
	}
	return nil
}

// RenderPage renders the current page to the provided writer with the current font and alignment
func (r *EPUBReader) RenderPage(w io.Writer) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Clear screen and move cursor to top-left if it's a terminal
	if f, ok := w.(*os.File); ok && term.IsTerminal(int(f.Fd())) {
		fmt.Fprint(w, "\033[H\033[2J")
	}

	// Get terminal dimensions
	width := 80  // Default width
	height := 24 // Default height
	if f, ok := w.(*os.File); ok && term.IsTerminal(int(f.Fd())) {
		var err error
		width, height, err = term.GetSize(int(f.Fd()))
		if err != nil {
			width = 80
			height = 24
		}
	}

	// Get current page content
	pageText := r.CurrentPage()

	// Process the text with current alignment
	contentWidth := width - 2 // Leave 1 char margin on each side
	// NOTE: wrapText is an external helper function not included in this file.
	// Its correct implementation is crucial for formatting the text.
	lines := wrapText(pageText, contentWidth)

	// Calculate how many lines we can show (leave room for status and margin)
	maxLines := height - 4
	if len(lines) > maxLines {
		lines = lines[:maxLines]
	}

	// Print the content with proper formatting
	for _, line := range lines {
		line = strings.TrimRight(line, " ") // Trim any trailing spaces
		lineWidth := runewidth.StringWidth(line)

		// Handle different line types with formatting
		switch {
		case isHeading(line):
			// Center headings
			// NOTE: isHeading and centerText are external helper functions not included in this file.
			// Their correct implementation is crucial for formatting headings.
			bold := color.New(color.Bold)
			centeredLine := centerText(line, contentWidth, " ")
			bold.Println(w, " "+centeredLine) // Add leading space for margin

		case strings.HasPrefix(strings.TrimSpace(line), "•"):
			// Format bullet points
			fmt.Fprintf(w, "  %s\n", line)

		case strings.TrimSpace(line) == "":
			// Empty line
			fmt.Fprintln(w)

		default:
			// Regular text - apply justification if needed
			// NOTE: justifyText is an external helper function not included in this file.
			// Its correct implementation is crucial for justifying text.
			if r.align == AlignJustify && lineWidth < contentWidth && len(strings.Fields(line)) > 1 {
				line = justifyText(line, contentWidth)
			}
			fmt.Fprintf(w, " %s\n", line) // Add leading space for margin
		}
	}

	// Add enhanced status line at the bottom
	page, total := r.PageInfo()
	layoutStr := "Paged"
	if r.layout == LayoutScrolled {
		layoutStr = "Scrolled"
	}
	alignStr := "Left"
	if r.align == AlignJustify {
		alignStr = "Justify"
	}

	// Enhanced status with more navigation options
	status := fmt.Sprintf("Page %d/%d | %s | %s | (n:next p:prev N:P:chapters j:k:scroll f:font a:align m:layout s:search ?:help q:quit)",
		page, total, layoutStr, alignStr)
	status = centerText(status, width, "=")

	// Print some empty lines to push status to bottom
	for i := len(lines); i < maxLines; i++ {
		fmt.Fprintln(w)
	}

	// Print status in a different color
	statusColor := color.New(color.BgBlue, color.FgWhite, color.Bold)
	statusColor.Fprintln(w, status)

	return nil
}
