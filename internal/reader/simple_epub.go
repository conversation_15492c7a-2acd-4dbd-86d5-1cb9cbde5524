package reader

import (
	"archive/zip"
	"encoding/xml"
	"fmt"
	"io"
	"path/filepath"
	"strings"
)

// SimpleEPUB represents a simple EPUB reader that doesn't rely on external libraries
type SimpleEPUB struct {
	zipReader *zip.ReadCloser
	container Container
	opf       OPF
	spine     []SpineItem
	manifest  map[string]ManifestItem
}

// Container represents the META-INF/container.xml structure
type Container struct {
	XMLName   xml.Name `xml:"container"`
	Rootfiles []struct {
		FullPath  string `xml:"full-path,attr"`
		MediaType string `xml:"media-type,attr"`
	} `xml:"rootfiles>rootfile"`
}

// OPF represents the content.opf structure
type OPF struct {
	XMLName  xml.Name `xml:"package"`
	Metadata struct {
		Title   string `xml:"title"`
		Creator string `xml:"creator"`
	} `xml:"metadata"`
	Manifest struct {
		Items []ManifestItem `xml:"item"`
	} `xml:"manifest"`
	Spine struct {
		Items []SpineItem `xml:"itemref"`
	} `xml:"spine"`
}

// ManifestItem represents an item in the manifest
type ManifestItem struct {
	ID        string `xml:"id,attr"`
	HREF      string `xml:"href,attr"`
	MediaType string `xml:"media-type,attr"`
}

// SpineItem represents an item in the spine
type SpineItem struct {
	IDREF string `xml:"idref,attr"`
}

// NewSimpleEPUB creates a new simple EPUB reader
func NewSimpleEPUB(filePath string) (*SimpleEPUB, error) {
	// Open the EPUB file as a ZIP archive
	zipReader, err := zip.OpenReader(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open EPUB file: %v", err)
	}

	epub := &SimpleEPUB{
		zipReader: zipReader,
		manifest:  make(map[string]ManifestItem),
	}

	// Parse container.xml to find the OPF file
	if err := epub.parseContainer(); err != nil {
		zipReader.Close()
		return nil, fmt.Errorf("failed to parse container: %v", err)
	}

	// Parse the OPF file
	if err := epub.parseOPF(); err != nil {
		zipReader.Close()
		return nil, fmt.Errorf("failed to parse OPF: %v", err)
	}

	return epub, nil
}

// parseContainer reads and parses the META-INF/container.xml file
func (e *SimpleEPUB) parseContainer() error {
	for _, file := range e.zipReader.File {
		if file.Name == "META-INF/container.xml" {
			rc, err := file.Open()
			if err != nil {
				return err
			}
			defer rc.Close()

			data, err := io.ReadAll(rc)
			if err != nil {
				return err
			}

			return xml.Unmarshal(data, &e.container)
		}
	}
	return fmt.Errorf("container.xml not found")
}

// parseOPF reads and parses the OPF file
func (e *SimpleEPUB) parseOPF() error {
	if len(e.container.Rootfiles) == 0 {
		return fmt.Errorf("no rootfiles found in container")
	}

	opfPath := e.container.Rootfiles[0].FullPath

	for _, file := range e.zipReader.File {
		if file.Name == opfPath {
			rc, err := file.Open()
			if err != nil {
				return err
			}
			defer rc.Close()

			data, err := io.ReadAll(rc)
			if err != nil {
				return err
			}

			if err := xml.Unmarshal(data, &e.opf); err != nil {
				return err
			}

			// Build manifest map for quick lookup
			for _, item := range e.opf.Manifest.Items {
				e.manifest[item.ID] = item
			}

			// Store spine items
			e.spine = e.opf.Spine.Items

			return nil
		}
	}
	return fmt.Errorf("OPF file not found: %s", opfPath)
}

// GetTitle returns the book title
func (e *SimpleEPUB) GetTitle() string {
	return e.opf.Metadata.Title
}

// GetAuthor returns the book author
func (e *SimpleEPUB) GetAuthor() string {
	return e.opf.Metadata.Creator
}

// GetSpineCount returns the number of spine items
func (e *SimpleEPUB) GetSpineCount() int {
	return len(e.spine)
}

// GetSpineItem returns the content of a spine item by index
func (e *SimpleEPUB) GetSpineItem(index int) (string, error) {
	if index < 0 || index >= len(e.spine) {
		return "", fmt.Errorf("spine index out of range")
	}

	spineItem := e.spine[index]
	manifestItem, exists := e.manifest[spineItem.IDREF]
	if !exists {
		return "", fmt.Errorf("manifest item not found: %s", spineItem.IDREF)
	}

	// Get the base path from the OPF file location
	opfPath := e.container.Rootfiles[0].FullPath
	basePath := filepath.Dir(opfPath)

	// Construct the full path to the content file
	contentPath := filepath.Join(basePath, manifestItem.HREF)

	// Find and read the content file
	for _, file := range e.zipReader.File {
		if file.Name == contentPath {
			rc, err := file.Open()
			if err != nil {
				return "", err
			}
			defer rc.Close()

			data, err := io.ReadAll(rc)
			if err != nil {
				return "", err
			}

			return string(data), nil
		}
	}

	return "", fmt.Errorf("content file not found: %s", contentPath)
}

// Close closes the EPUB reader
func (e *SimpleEPUB) Close() error {
	if e.zipReader != nil {
		return e.zipReader.Close()
	}
	return nil
}

// GetAllPages extracts all readable content from the EPUB
func (e *SimpleEPUB) GetAllPages() ([]string, error) {
	var pages []string

	for i := 0; i < e.GetSpineCount(); i++ {
		content, err := e.GetSpineItem(i)
		if err != nil {
			continue // Skip items with errors
		}

		// Convert HTML to text using simple method
		text := basicHtmlToText(content)

		// Only add non-empty pages
		if len(strings.TrimSpace(text)) > 0 {
			pages = append(pages, text)
		}
	}

	return pages, nil
}

// basicHtmlToText provides a basic HTML to text conversion for the SimpleEPUB reader
func basicHtmlToText(htmlStr string) string {
	// Remove script and style content first
	text := htmlStr

	// Simple tag removal - remove everything between < and >
	var result strings.Builder
	inTag := false
	for _, r := range text {
		switch r {
		case '<':
			inTag = true
		case '>':
			inTag = false
		default:
			if !inTag {
				result.WriteRune(r)
			}
		}
	}

	text = result.String()

	// Basic cleanup
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")

	// Normalize whitespace
	lines := strings.Split(text, "\n")
	var cleanLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}

	return strings.Join(cleanLines, "\n")
}
