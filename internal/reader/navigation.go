package reader

import (
	"fmt"
)

// NavigationAction represents a navigation action
type NavigationAction int

const (
	ActionNone NavigationAction = iota
	ActionNextPage
	ActionPrevPage
	ActionNextChapter
	ActionPrevChapter
	ActionGotoPage
	ActionGotoChapter
	ActionSearch
	ActionQuit
	ActionHelp
	ActionToggleFont
	ActionToggleAlignment
	ActionToggleLayout
	ActionScrollUp
	ActionScrollDown
	ActionGotoTop
	ActionGotoBottom
)

// NavigationHandler handles navigation input
type NavigationHandler struct {
	reader *EPUBReader
}

// NewNavigationHandler creates a new navigation handler
func NewNavigationHandler(reader *EPUBReader) *NavigationHandler {
	return &NavigationHandler{
		reader: reader,
	}
}

// HandleKey processes a key press and returns the corresponding action
func (nh *NavigationHandler) HandleKey(key rune) NavigationAction {
	switch key {
	case 'q', 'Q':
		return ActionQuit
	case 'n', ' ':
		return ActionNextPage
	case 'p', 'b':
		return ActionPrevPage
	case 'N':
		return ActionNextChapter
	case 'P':
		return ActionPrevChapter
	case 'g':
		return ActionGotoPage
	case 'G':
		return ActionGotoBottom
	case 'k':
		return ActionScrollUp
	case 'j':
		return ActionScrollDown
	case 'h':
		return ActionGotoTop
	case 'l':
		return ActionGotoBottom
	case 'f':
		return ActionToggleFont
	case 'a':
		return ActionToggleAlignment
	case 'm':
		return ActionToggleLayout
	case 's':
		return ActionSearch
	case '?':
		return ActionHelp
	default:
		return ActionNone
	}
}

// ExecuteAction executes a navigation action
func (nh *NavigationHandler) ExecuteAction(action NavigationAction) bool {
	switch action {
	case ActionQuit:
		return false // Signal to quit
	case ActionNextPage:
		return nh.reader.NextPage()
	case ActionPrevPage:
		return nh.reader.PrevPage()
	case ActionNextChapter:
		return nh.reader.NextChapter()
	case ActionPrevChapter:
		return nh.reader.PrevChapter()
	case ActionScrollUp:
		return nh.reader.ScrollUp()
	case ActionScrollDown:
		return nh.reader.ScrollDown()
	case ActionGotoTop:
		return nh.reader.GotoTop()
	case ActionGotoBottom:
		return nh.reader.GotoBottom()
	case ActionToggleFont:
		nh.reader.CycleFont()
		return true
	case ActionToggleAlignment:
		nh.reader.CycleAlignment()
		return true
	case ActionToggleLayout:
		nh.reader.CycleLayout()
		return true
	case ActionSearch:
		nh.reader.Search()
		return true
	case ActionHelp:
		nh.reader.ShowHelp()
		return true
	default:
		return true
	}
}

// GetHelpText returns the help text for keybindings
func (nh *NavigationHandler) GetHelpText() string {
	return `Navigation Help:
  n, space    - Next page
  p, b        - Previous page
  N           - Next chapter
  P           - Previous chapter
  j           - Scroll down
  k           - Scroll up
  g           - Go to page
  G           - Go to bottom
  h           - Go to top
  l           - Go to bottom
  f           - Change font
  a           - Toggle alignment
  m           - Toggle layout mode
  s           - Search
  ?           - Show this help
  q           - Quit`
}

// SearchResult represents a search result
type SearchResult struct {
	PageNum int
	LineNum int
	Text    string
	Context string
}

// Search performs a text search in the EPUB
func (nh *NavigationHandler) Search() []SearchResult {
	// This is a placeholder for search functionality
	// In a full implementation, you would:
	// 1. Prompt user for search term
	// 2. Search through all pages
	// 3. Return results with context
	return []SearchResult{}
}

// Enhanced EPUBReader methods for navigation

// NextChapter moves to the next chapter
func (r *EPUBReader) NextChapter() bool {
	// For now, treat each page as a chapter
	// In a full implementation, you would detect chapter boundaries
	return r.NextPage()
}

// PrevChapter moves to the previous chapter
func (r *EPUBReader) PrevChapter() bool {
	// For now, treat each page as a chapter
	return r.PrevPage()
}

// ScrollUp scrolls up within the current page
func (r *EPUBReader) ScrollUp() bool {
	// This would be implemented for scrolled layout mode
	return true
}

// ScrollDown scrolls down within the current page
func (r *EPUBReader) ScrollDown() bool {
	// This would be implemented for scrolled layout mode
	return true
}

// GotoTop goes to the top of the current page
func (r *EPUBReader) GotoTop() bool {
	// This would be implemented for scrolled layout mode
	return true
}

// GotoBottom goes to the bottom of the current page
func (r *EPUBReader) GotoBottom() bool {
	// This would be implemented for scrolled layout mode
	return true
}

// CycleFont cycles through available fonts
func (r *EPUBReader) CycleFont() {
	// This would cycle through available fonts
	// For now, just toggle between default and a few options
	fonts := []string{"", "mono", "serif"}
	currentFont := r.font

	for i, font := range fonts {
		if font == currentFont {
			nextFont := fonts[(i+1)%len(fonts)]
			r.SetFont(nextFont)
			break
		}
	}
}

// CycleAlignment cycles through alignment options
func (r *EPUBReader) CycleAlignment() {
	currentAlign := r.Align()
	if currentAlign == AlignLeft {
		r.SetAlign(AlignJustify)
	} else {
		r.SetAlign(AlignLeft)
	}
}

// CycleLayout cycles through layout modes
func (r *EPUBReader) CycleLayout() {
	currentLayout := r.Layout()
	if currentLayout == LayoutPaged {
		r.SetLayout(LayoutScrolled)
	} else {
		r.SetLayout(LayoutPaged)
	}
}

// Search performs a search (placeholder)
func (r *EPUBReader) Search() {
	// Placeholder for search functionality
	fmt.Println("Search functionality not yet implemented")
}

// ShowHelp shows the help screen
func (r *EPUBReader) ShowHelp() {
	// This would show a help screen
	// For now, just print help text
	fmt.Println("Help screen - press any key to continue")
}
