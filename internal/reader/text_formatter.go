package reader

import (
	"regexp"
	"strings"

	"github.com/mattn/go-runewidth"
)

// cleanText removes extra whitespace and normalizes the text
func cleanText(text string) string {
	// Replace multiple spaces with a single space
	re := regexp.MustCompile(`\s+`)
	text = re.ReplaceAllString(text, " ")
	// Remove leading/trailing whitespace
	return strings.TrimSpace(text)
}

// isHeading checks if a line looks like a heading
func isHeading(line string) bool {
	return len(line) > 0 && (strings.HasPrefix(line, "#") ||
		strings.HasPrefix(line, "Chapter ") ||
		strings.HasPrefix(line, "Part ") ||
		strings.HasPrefix(line, "Section "))
}

// justifyText justifies a line to the specified width.
func justifyText(line string, targetWidth int) string {
	currentWidth := runewidth.StringWidth(line)
	if currentWidth >= targetWidth {
		return line // Already wide enough or too wide
	}

	spacesToAdd := targetWidth - currentWidth
	return addSpacesForJustification(line, spacesToAdd)
}

// addSpacesForJustification distributes spaces evenly between words to justify a line.
func addSpacesForJustification(line string, spacesToAdd int) string {
	words := strings.Fields(line)
	numWords := len(words)

	if numWords <= 1 || spacesToAdd <= 0 {
		return line // Nothing to justify or no spaces to add
	}

	gaps := numWords - 1
	if gaps == 0 {
		return line
	}

	// Calculate base spaces and remainder
	baseExtraSpaces := spacesToAdd / gaps
	remainderSpaces := spacesToAdd % gaps

	var result strings.Builder
	for i, word := range words {
		result.WriteString(word)
		if i < gaps { // Not the last word
			currentGapSpaces := 1 + baseExtraSpaces // Always at least one space
			if i < remainderSpaces {
				currentGapSpaces++ // Distribute remainder spaces one by one
			}
			result.WriteString(strings.Repeat(" ", currentGapSpaces))
		}
	}
	return result.String()
}

// wrapText implements simple word wrapping. It breaks text into lines
// based on the given width, without applying any alignment or special formatting.
func wrapText(text string, width int) []string {
	if width <= 10 {
		width = 10
	}

	var result []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		line = strings.TrimRight(line, " \t")
		if line == "" {
			// Preserve single empty lines between paragraphs
			if len(result) == 0 || result[len(result)-1] != "" {
				result = append(result, "")
			}
			continue
		}

		// Handle indented text (code blocks, quotes, etc.) - preserve as-is but wrap if too long
		if strings.HasPrefix(line, "    ") || strings.HasPrefix(line, "\t") ||
			strings.HasPrefix(line, "  ") || strings.HasPrefix(line, ">") {

			// If the indented line is too long, wrap it while preserving indentation
			if runewidth.StringWidth(line) > width {
				prefix := ""
				content := line
				if strings.HasPrefix(line, "    ") {
					prefix = "    "
					content = strings.TrimPrefix(line, "    ")
				} else if strings.HasPrefix(line, "\t") {
					prefix = "\t"
					content = strings.TrimPrefix(line, "\t")
				} else if strings.HasPrefix(line, "  ") {
					prefix = "  "
					content = strings.TrimPrefix(line, "  ")
				} else if strings.HasPrefix(line, ">") {
					prefix = "> "
					content = strings.TrimPrefix(line, ">")
					content = strings.TrimPrefix(content, " ")
				}

				// Wrap the content part
				wrappedContent := wrapText(content, width-runewidth.StringWidth(prefix))
				for i, wrappedLine := range wrappedContent {
					if i == 0 {
						result = append(result, prefix+wrappedLine)
					} else {
						result = append(result, prefix+wrappedLine)
					}
				}
			} else {
				result = append(result, line)
			}
			continue
		}

		// Simple word wrapping
		words := strings.Fields(line)
		if len(words) == 0 {
			continue
		}

		currentLine := words[0]
		lineWidth := runewidth.StringWidth(words[0])

		for _, word := range words[1:] {
			wordWidth := runewidth.StringWidth(word)
			spaceWidth := 1 // Single space between words

			if lineWidth+spaceWidth+wordWidth > width {
				result = append(result, currentLine)
				currentLine = word
				lineWidth = wordWidth
			} else {
				currentLine += " " + word
				lineWidth += spaceWidth + wordWidth
			}
		}

		if currentLine != "" {
			result = append(result, currentLine)
		}
	}

	return result
}

// centerText centers text within a line of specified width using the given fill character.
// It properly handles wide characters and ensures the text is centered both visually and mathematically.
func centerText(text string, width int, fillChar string) string {
	if width <= 0 {
		return text
	}

	// Calculate the display width of the text
	textWidth := runewidth.StringWidth(text)

	// If the text is wider than the available width, return it as-is
	if textWidth >= width {
		return text
	}

	// Calculate the padding needed on each side
	totalPadding := width - textWidth
	leftPadding := totalPadding / 2
	rightPadding := totalPadding - leftPadding

	// Create the padding strings
	leftPad := ""
	if leftPadding > 0 {
		leftPad = strings.Repeat(fillChar, leftPadding)
	}

	rightPad := ""
	if rightPadding > 0 {
		rightPad = strings.Repeat(fillChar, rightPadding)
	}

	return leftPad + text + rightPad
}
