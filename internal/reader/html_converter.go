package reader

import (
	"bytes"
	"fmt"
	"os"
	"regexp"
	"strings"

	"golang.org/x/net/html"
)

// htmlToText converts HTML to plain text for terminal display, handling complex structure using a DOM parser.
func htmlToText(htmlStr string) string {
	fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: Input HTML length = %d\n", len(htmlStr))

	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: HTML parsing failed: %v\n", err)
		// fallback to old method if parsing fails
		return fallbackHtmlToText(htmlStr)
	}

	var buf bytes.Buffer

	// Find the body element, or if no body exists, render the entire document
	body := findBodyNode(doc)
	if body != nil {
		fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: Found body element, rendering body content\n")
		renderNode(&buf, body, 0, false)
	} else {
		fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: No body element found, rendering entire document\n")
		renderNode(&buf, doc, 0, false)
	}

	text := buf.String()

	fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: Raw extracted text length = %d\n", len(text))

	// If DOM parsing and rendering produced no text, try the basic text extraction fallback.
	if text == "" {
		fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: DOM rendering produced empty text, trying basic extraction fallback\n")
		text = extractAnyText(htmlStr)
	}

	// Collapse multiple blank lines and spaces
	text = regexp.MustCompile(`\n{3,}`).ReplaceAllString(text, "\n\n")
	text = regexp.MustCompile(` +`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: Final text length = %d\n", len(text))

	// The check after renderNode should handle cases where text is empty, so this final fallback might be redundant,
	// but keeping it for safety for now.
	// if text == "" {
	// 	fmt.Fprintf(os.Stderr, "[DEBUG] htmlToText: Text still empty after cleaning, trying final fallback extraction\n")
	// 	text = extractAnyText(htmlStr)
	// }

	return text
}

// findBodyNode recursively searches for the body element in the HTML document
func findBodyNode(n *html.Node) *html.Node {
	if n.Type == html.ElementNode && n.Data == "body" {
		return n
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if body := findBodyNode(c); body != nil {
			return body
		}
	}

	return nil
}

// extractAnyText is a very basic fallback that tries to extract any text from HTML
func extractAnyText(htmlStr string) string {
	// Remove all HTML tags and decode entities
	text := htmlStr

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// Remove all HTML tags
	var result strings.Builder
	inTag := false
	for _, r := range text {
		switch r {
		case '<':
			inTag = true
		case '>':
			inTag = false
		default:
			if !inTag {
				result.WriteRune(r)
			}
		}
	}

	text = result.String()

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// fallbackHtmlToText is the previous implementation, used as a backup.
func fallbackHtmlToText(html string) string {
	replacer := strings.NewReplacer(
		"<br>", "\n",
		"<br/>", "\n",
		"<br />", "\n",
		"<p>", "\n\n",
		"</p>", "",
		"<h1>", "\n\n# ",
		"</h1>", "\n\n",
		"<h2>", "\n\n## ",
		"</h2>", "\n\n",
		"<h3>", "\n\n### ",
		"</h3>", "\n\n",
		"<ul>", "\n",
		"</ul>", "\n", "<ol>", "\n",
		"</ol>", "\n",
		"<li>", "\n• ",
		"</li>", "",
		"blockquote", "\n> ",
		"</blockquote>", "\n", "<pre>", "\n\n",
		"</pre>", "\n\n",
		"<code>", "`",
		"</code>", "`",
		"<hr>", "\n--------------------\n",
	)
	html = replacer.Replace(html)

	// Handle inline tags: <b>, <strong>, <i>, <em>
	html = strings.ReplaceAll(html, "<b>", "*")
	html = strings.ReplaceAll(html, "</b>", "*")
	html = strings.ReplaceAll(html, "<strong>", "*")
	html = strings.ReplaceAll(html, "</strong>", "*")
	html = strings.ReplaceAll(html, "<i>", "_")
	html = strings.ReplaceAll(html, "</i>", "_")
	html = strings.ReplaceAll(html, "<em>", "_")
	html = strings.ReplaceAll(html, "</em>", "_")

	// Remove all other HTML tags
	var result strings.Builder
	inTag := false
	for _, r := range html {
		switch r {
		case '<':
			inTag = true
		case '>':
			inTag = false
		default:
			if !inTag {
				result.WriteRune(r)
			}
		}
	}

	text := result.String()

	// Collapse multiple blank lines
	text = regexp.MustCompile(`\n{3,}`).ReplaceAllString(text, "\n\n")
	// Collapse multiple spaces
	text = regexp.MustCompile(` +`).ReplaceAllString(text, " ")
	// Trim leading/trailing whitespace
	text = strings.TrimSpace(text)

	return text
}

// renderNode recursively renders an HTML node and its children to plain text.
func renderNode(buf *bytes.Buffer, n *html.Node, indent int, inList bool) {
	switch n.Type {
	case html.TextNode:
		// DEBUG: Print raw text node data
		fmt.Fprintf(os.Stderr, "[DEBUG] renderNode: Processing TextNode, raw data length = %d: %q\n", len(n.Data), n.Data)
		text := strings.TrimSpace(n.Data)
		if text != "" {
			buf.WriteString(text)
			// Add a space after text unless it's followed by punctuation or certain tags
			if n.NextSibling != nil && n.NextSibling.Type == html.ElementNode {
				switch n.NextSibling.Data {
				case "p", "div", "section", "br", "h1", "h2", "h3", "ul", "ol", "li", "blockquote", "pre", "code":
					// Don't add space before block-level elements
				default:
					// Add space after text for inline elements or other text
					buf.WriteString(" ")
				}
			} else if n.NextSibling == nil && n.Parent != nil && n.Parent.Type == html.ElementNode {
				// If this is the last child text node in an element, add a space if the parent is inline
				switch n.Parent.Data {
				case "b", "strong", "i", "em":
					// Add space after inline elements
					buf.WriteString(" ")
				}
			} else if n.NextSibling != nil && n.NextSibling.Type == html.TextNode {
				// Add space between consecutive text nodes
				buf.WriteString(" ")
			}
		}
	case html.ElementNode:
		switch n.Data {
		case "br":
			buf.WriteString("\n")
		case "p", "section": // Removed "div" from this case
			buf.WriteString("\n\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h1":
			buf.WriteString("\n\n# ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h2":
			buf.WriteString("\n\n## ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h3":
			buf.WriteString("\n\n### ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "ul":
			buf.WriteString("\n") // Added semicolon
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+2, true)
			}
			buf.WriteString("\n")
		case "ol":
			buf.WriteString("\n") // Added semicolon
			i := 1
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Data == "li" {
					buf.WriteString(fmt.Sprintf("%s%d. ", strings.Repeat(" ", indent), i))
					renderNode(buf, c, indent+2, false)
					buf.WriteString("\n")
					i++
				} else {
					renderNode(buf, c, indent+2, false)
				}
			}
			buf.WriteString("\n")
		case "li":
			if inList {
				buf.WriteString(fmt.Sprintf("%s• ", strings.Repeat(" ", indent)))
			}
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n")
		case "blockquote":
			buf.WriteString("\n> ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+2, false)
			}
			buf.WriteString("\n")
		case "pre", "code":
			buf.WriteString("\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+4, false)
			}
			buf.WriteString("\n")
		case "b", "strong":
			buf.WriteString("*")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("*")
		case "i", "em":
			buf.WriteString("_")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("_")
		case "a": // Handle links - extract text content
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		case "img": // Skip images
			// Do nothing
		case "figure": // Handle figure elements - extract content but skip images
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Type == html.ElementNode && c.Data == "img" {
					// Skip images in figures
					continue
				}
				renderNode(buf, c, indent, false)
			}
		case "body", "html": // Handle body and html elements - just render children
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		case "aside": // Handle aside elements (sidebars, notes) - render with some spacing
			buf.WriteString("\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n")
		case "span", "div": // Handle span and div as inline elements unless they contain block elements
			isBlock := false
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Type == html.ElementNode {
					switch c.Data {
					case "p", "div", "section", "br", "h1", "h2", "h3", "ul", "ol", "li", "blockquote", "pre", "code":
						isBlock = true
						break
					}
				}
				if isBlock {
					break
				}
			}
			if isBlock || n.Data == "div" { // Treat div as block by default
				buf.WriteString("\n") // Add newline for block-like behavior
				for c := n.FirstChild; c != nil; c = c.NextSibling {
					renderNode(buf, c, indent, false)
				}
				buf.WriteString("\n") // Add newline for block-like behavior
			} else { // Treat as inline
				for c := n.FirstChild; c != nil; c = c.NextSibling {
					renderNode(buf, c, indent, false)
				}
			}

		default:
			// DEBUG: Print unhandled element node
			fmt.Fprintf(os.Stderr, "[DEBUG] renderNode: Unhandled ElementNode: %s\n", n.Data)
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		}
	}
}
