package reader

import (
	"bytes"
	"fmt"
	"os"
	"regexp"
	"strings"

	"golang.org/x/net/html"
)

// htmlToText converts HTML to plain text for terminal display, using a simpler and more reliable approach.
func htmlToText(htmlStr string) string {
	if len(htmlStr) == 0 {
		return ""
	}

	// First try the simple replacement method which is faster and more reliable
	text := simpleHtmlToText(htmlStr)

	// If that produces reasonable output, use it
	if len(strings.TrimSpace(text)) > 10 {
		return text
	}

	// Otherwise, try DOM parsing as fallback
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		// If parsing fails, use the simple method
		return simpleHtmlToText(htmlStr)
	}

	var buf bytes.Buffer

	// Find the body element, or if no body exists, render the entire document
	body := findBodyNode(doc)
	if body != nil {
		renderNode(&buf, body, 0, false)
	} else {
		renderNode(&buf, doc, 0, false)
	}

	text = buf.String()

	// If DOM parsing produced no text, fall back to simple method
	if len(strings.TrimSpace(text)) == 0 {
		text = simpleHtmlToText(htmlStr)
	}

	// Clean up the text
	text = cleanupText(text)
	return text
}

// simpleHtmlToText provides a fast, reliable HTML to text conversion
func simpleHtmlToText(htmlStr string) string {
	// Remove script and style content first
	scriptRe := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	styleRe := regexp.MustCompile(`(?i)<style[^>]*>.*?</style>`)
	htmlStr = scriptRe.ReplaceAllString(htmlStr, "")
	htmlStr = styleRe.ReplaceAllString(htmlStr, "")

	// Handle common block elements
	replacements := map[string]string{
		`<br\s*/?>`:         "\n",
		`<p[^>]*>`:          "\n\n",
		`</p>`:              "",
		`<h[1-6][^>]*>`:     "\n\n",
		`</h[1-6]>`:         "\n\n",
		`<div[^>]*>`:        "\n",
		`</div>`:            "",
		`<li[^>]*>`:         "\n• ",
		`</li>`:             "",
		`<ul[^>]*>`:         "\n",
		`</ul>`:             "\n",
		`<ol[^>]*>`:         "\n",
		`</ol>`:             "\n",
		`<blockquote[^>]*>`: "\n> ",
		`</blockquote>`:     "\n",
		`<hr[^>]*>`:         "\n" + strings.Repeat("-", 40) + "\n",
	}

	for pattern, replacement := range replacements {
		re := regexp.MustCompile(`(?i)` + pattern)
		htmlStr = re.ReplaceAllString(htmlStr, replacement)
	}

	// Remove all remaining HTML tags
	tagRe := regexp.MustCompile(`<[^>]*>`)
	text := tagRe.ReplaceAllString(htmlStr, "")

	return cleanupText(text)
}

// cleanupText cleans up extracted text
func cleanupText(text string) string {
	// Decode HTML entities
	text = html.UnescapeString(text)

	// Normalize whitespace
	text = regexp.MustCompile(`[ \t]+`).ReplaceAllString(text, " ")
	text = regexp.MustCompile(`\n[ \t]+`).ReplaceAllString(text, "\n")
	text = regexp.MustCompile(`[ \t]+\n`).ReplaceAllString(text, "\n")

	// Collapse multiple newlines
	text = regexp.MustCompile(`\n{3,}`).ReplaceAllString(text, "\n\n")

	return strings.TrimSpace(text)
}

// TestHtmlToText exposes htmlToText for testing purposes
func TestHtmlToText(htmlStr string) string {
	return htmlToText(htmlStr)
}

// findBodyNode recursively searches for the body element in the HTML document
func findBodyNode(n *html.Node) *html.Node {
	if n.Type == html.ElementNode && n.Data == "body" {
		return n
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if body := findBodyNode(c); body != nil {
			return body
		}
	}

	return nil
}

// extractAnyText is a very basic fallback that tries to extract any text from HTML
func extractAnyText(htmlStr string) string {
	// Remove all HTML tags and decode entities
	text := htmlStr

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// Remove all HTML tags
	var result strings.Builder
	inTag := false
	for _, r := range text {
		switch r {
		case '<':
			inTag = true
		case '>':
			inTag = false
		default:
			if !inTag {
				result.WriteRune(r)
			}
		}
	}

	text = result.String()

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// fallbackHtmlToText is the previous implementation, used as a backup.
func fallbackHtmlToText(html string) string {
	replacer := strings.NewReplacer(
		"<br>", "\n",
		"<br/>", "\n",
		"<br />", "\n",
		"<p>", "\n\n",
		"</p>", "",
		"<h1>", "\n\n# ",
		"</h1>", "\n\n",
		"<h2>", "\n\n## ",
		"</h2>", "\n\n",
		"<h3>", "\n\n### ",
		"</h3>", "\n\n",
		"<ul>", "\n",
		"</ul>", "\n", "<ol>", "\n",
		"</ol>", "\n",
		"<li>", "\n• ",
		"</li>", "",
		"blockquote", "\n> ",
		"</blockquote>", "\n", "<pre>", "\n\n",
		"</pre>", "\n\n",
		"<code>", "`",
		"</code>", "`",
		"<hr>", "\n--------------------\n",
	)
	html = replacer.Replace(html)

	// Handle inline tags: <b>, <strong>, <i>, <em>
	html = strings.ReplaceAll(html, "<b>", "*")
	html = strings.ReplaceAll(html, "</b>", "*")
	html = strings.ReplaceAll(html, "<strong>", "*")
	html = strings.ReplaceAll(html, "</strong>", "*")
	html = strings.ReplaceAll(html, "<i>", "_")
	html = strings.ReplaceAll(html, "</i>", "_")
	html = strings.ReplaceAll(html, "<em>", "_")
	html = strings.ReplaceAll(html, "</em>", "_")

	// Remove all other HTML tags
	var result strings.Builder
	inTag := false
	for _, r := range html {
		switch r {
		case '<':
			inTag = true
		case '>':
			inTag = false
		default:
			if !inTag {
				result.WriteRune(r)
			}
		}
	}

	text := result.String()

	// Collapse multiple blank lines
	text = regexp.MustCompile(`\n{3,}`).ReplaceAllString(text, "\n\n")
	// Collapse multiple spaces
	text = regexp.MustCompile(` +`).ReplaceAllString(text, " ")
	// Trim leading/trailing whitespace
	text = strings.TrimSpace(text)

	return text
}

// renderNode recursively renders an HTML node and its children to plain text.
func renderNode(buf *bytes.Buffer, n *html.Node, indent int, inList bool) {
	switch n.Type {
	case html.TextNode:
		// DEBUG: Print raw text node data
		fmt.Fprintf(os.Stderr, "[DEBUG] renderNode: Processing TextNode, raw data length = %d: %q\n", len(n.Data), n.Data)
		text := strings.TrimSpace(n.Data)
		if text != "" {
			buf.WriteString(text)
			// Add a space after text unless it's followed by punctuation or certain tags
			if n.NextSibling != nil && n.NextSibling.Type == html.ElementNode {
				switch n.NextSibling.Data {
				case "p", "div", "section", "br", "h1", "h2", "h3", "ul", "ol", "li", "blockquote", "pre", "code":
					// Don't add space before block-level elements
				default:
					// Add space after text for inline elements or other text
					buf.WriteString(" ")
				}
			} else if n.NextSibling == nil && n.Parent != nil && n.Parent.Type == html.ElementNode {
				// If this is the last child text node in an element, add a space if the parent is inline
				switch n.Parent.Data {
				case "b", "strong", "i", "em":
					// Add space after inline elements
					buf.WriteString(" ")
				}
			} else if n.NextSibling != nil && n.NextSibling.Type == html.TextNode {
				// Add space between consecutive text nodes
				buf.WriteString(" ")
			}
		}
	case html.ElementNode:
		switch n.Data {
		case "br":
			buf.WriteString("\n")
		case "p", "section": // Removed "div" from this case
			buf.WriteString("\n\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h1":
			buf.WriteString("\n\n# ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h2":
			buf.WriteString("\n\n## ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "h3":
			buf.WriteString("\n\n### ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n\n")
		case "ul":
			buf.WriteString("\n") // Added semicolon
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+2, true)
			}
			buf.WriteString("\n")
		case "ol":
			buf.WriteString("\n") // Added semicolon
			i := 1
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Data == "li" {
					buf.WriteString(fmt.Sprintf("%s%d. ", strings.Repeat(" ", indent), i))
					renderNode(buf, c, indent+2, false)
					buf.WriteString("\n")
					i++
				} else {
					renderNode(buf, c, indent+2, false)
				}
			}
			buf.WriteString("\n")
		case "li":
			if inList {
				buf.WriteString(fmt.Sprintf("%s• ", strings.Repeat(" ", indent)))
			}
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n")
		case "blockquote":
			buf.WriteString("\n> ")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+2, false)
			}
			buf.WriteString("\n")
		case "pre", "code":
			buf.WriteString("\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent+4, false)
			}
			buf.WriteString("\n")
		case "b", "strong":
			buf.WriteString("*")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("*")
		case "i", "em":
			buf.WriteString("_")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("_")
		case "a": // Handle links - extract text content
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		case "img": // Skip images
			// Do nothing
		case "figure": // Handle figure elements - extract content but skip images
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Type == html.ElementNode && c.Data == "img" {
					// Skip images in figures
					continue
				}
				renderNode(buf, c, indent, false)
			}
		case "body", "html": // Handle body and html elements - just render children
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		case "aside": // Handle aside elements (sidebars, notes) - render with some spacing
			buf.WriteString("\n")
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
			buf.WriteString("\n")
		case "span", "div": // Handle span and div as inline elements unless they contain block elements
			isBlock := false
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				if c.Type == html.ElementNode {
					switch c.Data {
					case "p", "div", "section", "br", "h1", "h2", "h3", "ul", "ol", "li", "blockquote", "pre", "code":
						isBlock = true
						break
					}
				}
				if isBlock {
					break
				}
			}
			if isBlock || n.Data == "div" { // Treat div as block by default
				buf.WriteString("\n") // Add newline for block-like behavior
				for c := n.FirstChild; c != nil; c = c.NextSibling {
					renderNode(buf, c, indent, false)
				}
				buf.WriteString("\n") // Add newline for block-like behavior
			} else { // Treat as inline
				for c := n.FirstChild; c != nil; c = c.NextSibling {
					renderNode(buf, c, indent, false)
				}
			}

		default:
			// DEBUG: Print unhandled element node
			fmt.Fprintf(os.Stderr, "[DEBUG] renderNode: Unhandled ElementNode: %s\n", n.Data)
			for c := n.FirstChild; c != nil; c = c.NextSibling {
				renderNode(buf, c, indent, false)
			}
		}
	}
}
