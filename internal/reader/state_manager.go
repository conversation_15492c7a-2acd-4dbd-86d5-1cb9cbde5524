package reader

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// StateManager handles reading state persistence
type StateManager struct {
	configDir string
}

// NewStateManager creates a new state manager
func NewStateManager() *StateManager {
	configDir := getConfigDir()
	return &StateManager{
		configDir: configDir,
	}
}

// getConfigDir returns the configuration directory path
func getConfigDir() string {
	// Try XDG_CONFIG_HOME first
	if xdgConfig := os.Getenv("XDG_CONFIG_HOME"); xdgConfig != "" {
		return filepath.Join(xdgConfig, "epubcraft")
	}

	// Fallback to ~/.config/epubcraft
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return ".epubcraft" // Fallback to current directory
	}
	return filepath.Join(homeDir, ".config", "epubcraft")
}

// SaveState saves the current reading state
func (sm *StateManager) SaveState(filePath string, pageNum int, font string, align Alignment, layout LayoutMode) error {
	// Ensure config directory exists
	if err := os.MkdirAll(sm.configDir, 0o755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	state := ReadingState{
		FilePath:   filePath,
		PageNum:    pageNum,
		ChapterNum: pageNum, // For now, treat pages as chapters
		Font:       font,
		Alignment:  alignmentToString(align),
		Layout:     layoutToString(layout),
		LastRead:   time.Now().Format(time.RFC3339),
	}

	// Create a filename based on the EPUB file path
	stateFile := sm.getStateFileName(filePath)

	data, err := json.MarshalIndent(state, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal state: %v", err)
	}

	if err := os.WriteFile(stateFile, data, 0o644); err != nil {
		return fmt.Errorf("failed to write state file: %v", err)
	}

	return nil
}

// LoadState loads the reading state for a file
func (sm *StateManager) LoadState(filePath string) (*ReadingState, error) {
	stateFile := sm.getStateFileName(filePath)

	data, err := os.ReadFile(stateFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, nil // No saved state
		}
		return nil, fmt.Errorf("failed to read state file: %v", err)
	}

	var state ReadingState
	if err := json.Unmarshal(data, &state); err != nil {
		return nil, fmt.Errorf("failed to unmarshal state: %v", err)
	}

	return &state, nil
}

// getStateFileName creates a filename for the state file based on the EPUB path
func (sm *StateManager) getStateFileName(filePath string) string {
	// Create a hash or sanitized filename from the file path
	baseName := filepath.Base(filePath)
	// Remove extension and add .json
	stateName := strings.TrimSuffix(baseName, filepath.Ext(baseName)) + ".json"
	return filepath.Join(sm.configDir, stateName)
}

// alignmentToString converts Alignment to string
func alignmentToString(align Alignment) string {
	switch align {
	case AlignLeft:
		return "left"
	case AlignJustify:
		return "justify"
	default:
		return "left"
	}
}

// stringToAlignment converts string to Alignment
func stringToAlignment(s string) Alignment {
	switch s {
	case "justify":
		return AlignJustify
	default:
		return AlignLeft
	}
}

// layoutToString converts LayoutMode to string
func layoutToString(layout LayoutMode) string {
	switch layout {
	case LayoutPaged:
		return "paged"
	case LayoutScrolled:
		return "scrolled"
	default:
		return "paged"
	}
}

// stringToLayout converts string to LayoutMode
func stringToLayout(s string) LayoutMode {
	switch s {
	case "scrolled":
		return LayoutScrolled
	default:
		return LayoutPaged
	}
}
