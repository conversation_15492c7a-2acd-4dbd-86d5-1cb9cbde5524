package reader

// Alignment represents text alignment options
type Alignment int

const (
	AlignLeft Alignment = iota
	AlignJustify
)

// LayoutMode represents the reading layout mode
type LayoutMode int

const (
	LayoutPaged LayoutMode = iota
	LayoutScrolled
)

// ReadingState represents the current reading state for persistence
type ReadingState struct {
	FilePath   string `json:"file_path"`
	PageNum    int    `json:"page_num"`
	ChapterNum int    `json:"chapter_num"`
	Font       string `json:"font"`
	Alignment  string `json:"alignment"`
	Layout     string `json:"layout"`
	LastRead   string `json:"last_read"`
}
