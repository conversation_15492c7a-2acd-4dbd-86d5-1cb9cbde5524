package reader

import (
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/fatih/color"
)

// ProgressAnimationType represents different types of progress animations
type ProgressAnimationType int

const (
	ProgressBar ProgressAnimationType = iota
	ProgressDots
	ProgressSpinner
	ProgressWave
	ProgressPulse
)

// ProgressAnimator handles animated progress indicators
type ProgressAnimator struct {
	animType ProgressAnimationType
	width    int
	position int
	chars    []string
}

// NewProgressAnimator creates a new progress animator
func NewProgressAnimator(animType ProgressAnimationType, width int) *ProgressAnimator {
	pa := &ProgressAnimator{
		animType: animType,
		width:    width,
		position: 0,
	}
	
	switch animType {
	case ProgressSpinner:
		pa.chars = []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	case ProgressWave:
		pa.chars = []string{"▁", "▂", "▃", "▄", "▅", "▆", "▇", "█", "▇", "▆", "▅", "▄", "▃", "▂"}
	case ProgressPulse:
		pa.chars = []string{"●", "◐", "◑", "◒", "◓", "◔", "◕", "◖", "◗", "◘"}
	case ProgressDots:
		pa.chars = []string{"⠁", "⠂", "⠄", "⡀", "⢀", "⠠", "⠐", "⠈"}
	}
	
	return pa
}

// RenderProgressBar renders an animated progress bar
func (pa *ProgressAnimator) RenderProgressBar(w io.Writer, current, total int, message string) {
	if total == 0 {
		return
	}
	
	progress := float64(current) / float64(total)
	filledWidth := int(progress * float64(pa.width))
	
	// Create the progress bar
	var bar strings.Builder
	
	switch pa.animType {
	case ProgressBar:
		// Classic progress bar with animation
		for i := 0; i < pa.width; i++ {
			if i < filledWidth {
				if i == filledWidth-1 && current < total {
					// Animated leading edge
					bar.WriteString(pa.getAnimatedChar())
				} else {
					bar.WriteString("█")
				}
			} else {
				bar.WriteString("░")
			}
		}
		
	case ProgressWave:
		// Wave-style progress bar
		for i := 0; i < pa.width; i++ {
			if i < filledWidth {
				wavePos := (i + pa.position) % len(pa.chars)
				bar.WriteString(pa.chars[wavePos])
			} else {
				bar.WriteString("░")
			}
		}
		
	case ProgressDots:
		// Dotted progress bar
		for i := 0; i < pa.width; i++ {
			if i < filledWidth {
				if i == filledWidth-1 && current < total {
					dotPos := pa.position % len(pa.chars)
					bar.WriteString(pa.chars[dotPos])
				} else {
					bar.WriteString("●")
				}
			} else {
				bar.WriteString("○")
			}
		}
	}
	
	// Color the progress bar
	progressColor := color.New(color.FgGreen, color.Bold)
	percentage := int(progress * 100)
	
	fmt.Fprintf(w, "\r%s [%s] %d%% (%d/%d)", 
		message, 
		progressColor.Sprint(bar.String()), 
		percentage, 
		current, 
		total)
	
	pa.position++
}

// RenderSpinner renders an animated spinner
func (pa *ProgressAnimator) RenderSpinner(w io.Writer, message string) {
	if len(pa.chars) == 0 {
		return
	}
	
	spinnerChar := pa.chars[pa.position%len(pa.chars)]
	spinnerColor := color.New(color.FgCyan, color.Bold)
	
	fmt.Fprintf(w, "\r%s %s", spinnerColor.Sprint(spinnerChar), message)
	
	pa.position++
}

// getAnimatedChar returns an animated character for the progress bar edge
func (pa *ProgressAnimator) getAnimatedChar() string {
	chars := []string{"▏", "▎", "▍", "▌", "▋", "▊", "▉", "█"}
	return chars[pa.position%len(chars)]
}

// StatusBarAnimator handles animated status bars
type StatusBarAnimator struct {
	width      int
	scrollPos  int
	blinkState bool
	lastUpdate time.Time
}

// NewStatusBarAnimator creates a new status bar animator
func NewStatusBarAnimator(width int) *StatusBarAnimator {
	return &StatusBarAnimator{
		width:      width,
		scrollPos:  0,
		blinkState: false,
		lastUpdate: time.Now(),
	}
}

// RenderAnimatedStatus renders an animated status bar
func (sba *StatusBarAnimator) RenderAnimatedStatus(w io.Writer, status string, page, total int) {
	now := time.Now()
	
	// Update blink state every 500ms
	if now.Sub(sba.lastUpdate) > 500*time.Millisecond {
		sba.blinkState = !sba.blinkState
		sba.lastUpdate = now
	}
	
	// Create progress indicator
	progress := float64(page) / float64(total)
	progressWidth := sba.width - len(status) - 10 // Leave space for status and percentage
	if progressWidth < 10 {
		progressWidth = 10
	}
	
	filledWidth := int(progress * float64(progressWidth))
	
	var progressBar strings.Builder
	for i := 0; i < progressWidth; i++ {
		if i < filledWidth {
			progressBar.WriteString("▓")
		} else if i == filledWidth && sba.blinkState {
			progressBar.WriteString("▒")
		} else {
			progressBar.WriteString("░")
		}
	}
	
	// Color coding
	statusColor := color.New(color.BgBlue, color.FgWhite, color.Bold)
	progressColor := color.New(color.FgGreen)
	percentageColor := color.New(color.FgYellow, color.Bold)
	
	percentage := int(progress * 100)
	
	// Render the complete status bar
	statusColor.Fprintf(w, " %s ", status)
	fmt.Fprint(w, " ")
	progressColor.Fprint(w, progressBar.String())
	fmt.Fprint(w, " ")
	percentageColor.Fprintf(w, "%3d%% ", percentage)
}

// TextAnimator handles text reveal animations
type TextAnimator struct {
	revealSpeed time.Duration
	currentPos  int
}

// NewTextAnimator creates a new text animator
func NewTextAnimator(speed time.Duration) *TextAnimator {
	return &TextAnimator{
		revealSpeed: speed,
		currentPos:  0,
	}
}

// AnimateTextReveal reveals text progressively
func (ta *TextAnimator) AnimateTextReveal(w io.Writer, text string, lineByLine bool) {
	if lineByLine {
		lines := strings.Split(text, "\n")
		for i, line := range lines {
			fmt.Fprintf(w, "\033[%d;1H", i+1) // Move to line
			ta.animateLine(w, line)
		}
	} else {
		ta.animateLine(w, text)
	}
}

// animateLine animates a single line of text
func (ta *TextAnimator) animateLine(w io.Writer, line string) {
	for i, char := range line {
		fmt.Fprint(w, string(char))
		
		// Add some randomness to the reveal speed
		delay := ta.revealSpeed
		if char == ' ' {
			delay = delay / 3 // Spaces appear faster
		} else if strings.ContainsRune(".,!?;:", char) {
			delay = delay * 2 // Punctuation appears slower for dramatic effect
		}
		
		if i < len(line)-1 { // Don't sleep after the last character
			time.Sleep(delay)
		}
	}
}

// ButtonAnimator handles button press animations
type ButtonAnimator struct {
	pressedState map[string]bool
	pressTime    map[string]time.Time
}

// NewButtonAnimator creates a new button animator
func NewButtonAnimator() *ButtonAnimator {
	return &ButtonAnimator{
		pressedState: make(map[string]bool),
		pressTime:    make(map[string]time.Time),
	}
}

// AnimateButtonPress animates a button press
func (ba *ButtonAnimator) AnimateButtonPress(w io.Writer, buttonText string, x, y int) {
	// Move cursor to button position
	fmt.Fprintf(w, "\033[%d;%dH", y, x)
	
	// Pressed state (inverted colors)
	pressedColor := color.New(color.BgWhite, color.FgBlack, color.Bold)
	pressedColor.Fprint(w, buttonText)
	
	// Brief pause
	time.Sleep(100 * time.Millisecond)
	
	// Normal state
	fmt.Fprintf(w, "\033[%d;%dH", y, x)
	normalColor := color.New(color.BgBlue, color.FgWhite)
	normalColor.Fprint(w, buttonText)
}

// LoadingAnimator handles various loading animations
type LoadingAnimator struct {
	animType string
	frame    int
	frames   []string
}

// NewLoadingAnimator creates a new loading animator
func NewLoadingAnimator(animType string) *LoadingAnimator {
	la := &LoadingAnimator{
		animType: animType,
		frame:    0,
	}
	
	switch animType {
	case "dots":
		la.frames = []string{"   ", ".  ", ".. ", "...", " ..", "  .", "   "}
	case "bounce":
		la.frames = []string{"●    ", " ●   ", "  ●  ", "   ● ", "    ●", "   ● ", "  ●  ", " ●   "}
	case "pulse":
		la.frames = []string{"●", "◐", "○", "◑"}
	case "clock":
		la.frames = []string{"🕐", "🕑", "🕒", "🕓", "🕔", "🕕", "🕖", "🕗", "🕘", "🕙", "🕚", "🕛"}
	default:
		la.frames = []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	}
	
	return la
}

// NextFrame returns the next animation frame
func (la *LoadingAnimator) NextFrame() string {
	if len(la.frames) == 0 {
		return ""
	}
	
	frame := la.frames[la.frame%len(la.frames)]
	la.frame++
	return frame
}

// RenderLoading renders a loading animation
func (la *LoadingAnimator) RenderLoading(w io.Writer, message string) {
	frame := la.NextFrame()
	loadingColor := color.New(color.FgCyan, color.Bold)
	fmt.Fprintf(w, "\r%s %s", loadingColor.Sprint(frame), message)
}
