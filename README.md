# EPUBCraft

A CLI application for reading EPUB files and converting PDFs to EPUB format, built with Go.

## Features

- Read EPUB files directly in the terminal
- Convert PDF files to EPUB format
- Beautiful terminal interface with throbber animations
- Cross-platform support (Linux, macOS, Windows)

## Installation

1. Make sure you have [Go](https://golang.org/dl/) installed (version 1.16 or higher)
2. Clone this repository
3. Build and install the application:

```bash
go install github.com/yourusername/epubcraft/cmd/epubcraft@latest
```

## Usage

### Read an EPUB file

```bash
epubcraft read book.epub
```

### Convert a PDF to EPUB

```bash
epubcraft convert document.pdf output.epub
```

If no output filename is provided, it will use the input filename with a .epub extension.

### Show help

```bash
epubcraft --help
```

## Building from Source

1. Clone the repository
2. Build the application:

```bash
go build -o epubcraft ./cmd/epubcraft
```

## License

MIT

## Contributing

Contributions are welcome! Please open an issue or submit a pull request.
