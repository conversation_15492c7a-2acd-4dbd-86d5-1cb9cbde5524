package main

import (
	"fmt"
	"os"
	"time"

	"github.com/yourusername/epubcraft/internal/reader"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run simple_epub_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing simple EPUB reader with: %s\n", epubFile)

	start := time.Now()
	
	// Try to open the EPUB file with our simple reader
	fmt.Println("Opening EPUB file...")
	epub, err := reader.NewSimpleEPUB(epubFile)
	if err != nil {
		fmt.Printf("Error opening EPUB file: %v\n", err)
		os.Exit(1)
	}
	defer epub.Close()

	fmt.Printf("✓ EPUB opened successfully in %v\n", time.Since(start))

	// Get metadata
	fmt.Printf("✓ Title: %s\n", epub.GetTitle())
	fmt.Printf("✓ Author: %s\n", epub.GetAuthor())
	fmt.Printf("✓ Spine items: %d\n", epub.GetSpineCount())

	// Test reading first few spine items
	for i := 0; i < min(3, epub.GetSpineCount()); i++ {
		fmt.Printf("Reading spine item %d...\n", i+1)
		
		start := time.Now()
		content, err := epub.GetSpineItem(i)
		if err != nil {
			fmt.Printf("  ✗ Failed to read spine item %d: %v\n", i+1, err)
			continue
		}

		fmt.Printf("  ✓ Read %d bytes in %v\n", len(content), time.Since(start))
		
		// Show a preview of the content
		preview := content
		if len(preview) > 200 {
			preview = preview[:200] + "..."
		}
		fmt.Printf("  Content preview: %s\n", preview)
	}

	// Test getting all pages
	fmt.Println("Extracting all pages...")
	start = time.Now()
	pages, err := epub.GetAllPages()
	if err != nil {
		fmt.Printf("Error extracting pages: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✓ Extracted %d pages in %v\n", len(pages), time.Since(start))

	// Show preview of first page
	if len(pages) > 0 {
		preview := pages[0]
		if len(preview) > 300 {
			preview = preview[:300] + "..."
		}
		fmt.Printf("First page preview:\n%s\n", preview)
	}

	fmt.Println("✓ Simple EPUB reader test completed successfully!")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
