package cmd

import (
	"fmt"
	"os"

	"github.com/briandowns/spinner"
	"github.com/spf13/cobra"
)

var (
	// Used for flags
	verbose bool
	font       string
	alignStr   string
	layoutStr  string
	noJustify  bool
	globalSpinner *spinner.Spinner // Renamed from 's' to avoid conflict
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "epubcraft",
	Short: "EPUB reader and PDF to EPUB converter",
	Long: `EPUBCraft is a CLI application for reading EPUB files and 
converting PDF files to EPUB format with a beautiful terminal interface.`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	// Initialize spinner
	globalSpinner = spinner.New(spinner.CharSets[14], 100) // Build our new spinner

	// Global flags
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")
	rootCmd.PersistentFlags().StringVarP(&font, "font", "f", "", "Font to use for display (use 'list' to see available fonts)")
	rootCmd.PersistentFlags().StringVarP(&alignStr, "align", "a", "left", "Text alignment (left, justify)")
	rootCmd.PersistentFlags().StringVarP(&layoutStr, "layout", "l", "paged", "Layout mode (paged, scrolled)")
	rootCmd.PersistentFlags().BoolVar(&noJustify, "no-justify", false, "Disable text justification (always left-align)")
}
