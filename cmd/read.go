package cmd

import (
	"bufio"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"github.com/yourusername/epubcraft/internal/reader"
	"golang.org/x/term"
)

var fontFlag string

// List of available fonts that work well with go-figure
var availableFonts = func() []string {
	fonts := []string{
		"3-d", "3x5", "5lineoblique", "acrobatic", "alligator", "alligator2",
		"alphabet", "avatar", "banner", "banner3-D", "banner3", "banner4",
		"barbwire", "basic", "bell", "big", "bigchief", "binary", "block",
		"bubble", "bulbhead", "calgphy2", "caligraphy", "catwalk", "chunky",
		"coinstak", "colossal", "computer", "contessa", "contrast", "cosmic",
		"cosmike", "cricket", "cyberlarge", "cybermedium", "diamond", "digital",
		"doh", "doom", "dotmatrix", "double", "drpepper", "eftichess", "epic",
		"fender", "fourtops", "fuzzy", "goofy", "gothic", "graffiti", "hollywood",
		"invita", "isometric1", "isometric2", "isometric3", "isometric4", "italic",
		"ivrit", "jazmine", "jerusalem", "katakana", "kban", "larry3d", "lcd",
		"lean", "letters", "linux", "lockergnome", "madrid", "marquee", "mike",
		"mini", "mirror", "mnemonic", "morse", "moscow", "nancyj-fancy", "nancyj",
		"nancyj-underlined", "nipples", "ntgreek", "o8", "ogre", "pawp", "peaks",
		"pebbles", "pepper", "poison", "puffy", "pyramid", "rectangles", "relief",
		"rev", "roman", "rot13", "rounded", "rowancap", "rozzo", "runic",
		"runyc", "sblood", "script", "serifcap", "shadow", "short", "slant",
		"slide", "slscript", "small", "smisome1", "smkeyboard", "smscript",
		"smshadow", "smslant", "speed", "stampatello", "standard", "starwars",
		"stellar", "stop", "straight", "tanja", "tengwar", "term", "thick",
		"thin", "threepoint", "ticks", "ticksslant", "tinker-toy", "tombstone",
		"trek", "tsalagi", "twopoint", "univers", "weird", "whimsy",
	}
	sort.Strings(fonts)
	return fonts
}()

// readCmd represents the read command
var readCmd = &cobra.Command{
	Use:   "read [epub file]",
	Short: "Read an EPUB file",
	Long: `Read and display an EPUB file in the terminal with customizable formatting and animations.

Navigation:
  n, space: Next page
  p, b:     Previous page
  g:        Go to page
  f:        Change font
  a:        Toggle alignment (left/justify)
  m:        Toggle layout mode (paged/scrolled)
  x:        Toggle animations (on/off)
  t:        Change animation type
  q:        Quit`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		epubFile := args[0]

		// If no-justify flag is set, force left alignment
		if noJustify {
			alignStr = "left"
		}

		// Check if we should list fonts and exit
		if fontFlag == "list" {
			fmt.Println("Available fonts:")
			for i, f := range availableFonts {
				fmt.Printf("%3d. %s\n", i+1, f)
			}
			return nil
		}

		// Verify font if provided
		if fontFlag != "" {
			fontExists := false
			for _, f := range availableFonts {
				if f == fontFlag {
					fontExists = true
					break
				}
			}
			if !fontExists {
				color.Yellow("Warning: Font '%s' not found. Use 'list' to see available fonts\n", fontFlag)
				fontFlag = "" // Fall back to default font
			}
		}

		// Start spinner while loading
		globalSpinner.Prefix = "Loading EPUB "
		globalSpinner.Start()

		// Check if file exists
		if _, err := os.Stat(epubFile); os.IsNotExist(err) {
			globalSpinner.Stop()
			return fmt.Errorf("file %s does not exist", epubFile)
		}

		// Parse alignment
		var alignEnum reader.Alignment
		switch strings.ToLower(alignStr) {
		case "justify":
			alignEnum = reader.AlignJustify
		default:
			alignEnum = reader.AlignLeft
		}

		// Parse layout mode
		var layoutEnum reader.LayoutMode
		switch strings.ToLower(layoutStr) {
		case "scrolled":
			layoutEnum = reader.LayoutScrolled
		default:
			layoutEnum = reader.LayoutPaged
		}

		// Initialize the reader with the selected font if specified
		r, err := reader.NewEPUBReader(epubFile, fontFlag, alignEnum, layoutEnum)
		if err != nil {
			globalSpinner.Stop()
			return fmt.Errorf("error loading EPUB: %v", err)
		}
		// No Close method exists in the reader package, so we don't use defer r.Close()

		globalSpinner.Stop()

		// Set terminal to raw mode
		oldState, err := term.MakeRaw(int(os.Stdin.Fd()))
		if err != nil {
			fmt.Printf("Error setting terminal to raw mode: %v\n", err)
			return err
		}
		defer term.Restore(int(os.Stdin.Fd()), oldState)

		// Main read loop
		inputReader := bufio.NewReader(os.Stdin)
		for {
			// Clear screen and render current page
			fmt.Print("\033[H\033[2J") // Clear screen
			err = r.RenderPage(os.Stdout)
			if err != nil {
				fmt.Printf("Error rendering page: %v\n", err)
				return err
			}

			// Read user input
			char, _, err := inputReader.ReadRune()
			if err != nil {
				return err
			}

			switch char {
			case 'q', 'Q', '\x1b': // Q or ESC to quit
				fmt.Println("\nGoodbye!")
				return nil
			case 'n', ' ':
				r.NextPage()
			case 'p', 'b':
				r.PrevPage()
			case 'g':
				fmt.Print("\nGo to page: ")
				pageStr, _ := inputReader.ReadString('\n')
				page, err := strconv.Atoi(strings.TrimSpace(pageStr))
				if err == nil {
					r.GotoPage(page - 1) // Convert to 0-based
				}
			case 'f':
				fmt.Print("\nEnter font name (or 'list' to see available): ")
				fontName, _ := inputReader.ReadString('\n')
				fontName = strings.TrimSpace(fontName)
				if fontName == "list" {
					fmt.Println("\nAvailable fonts:")
					for _, f := range availableFonts {
						fmt.Printf("- %s\n", f)
					}
					fmt.Print("\nPress Enter to continue...")
					inputReader.ReadString('\n')
				} else {
					r.SetFont(fontName)
				}
			case 'a':
				// Toggle alignment
				currentAlign := r.Align()
				if currentAlign == reader.AlignLeft {
					r.SetAlign(reader.AlignJustify)
				} else {
					r.SetAlign(reader.AlignLeft)
				}
				r.RenderPage(os.Stdout) // Re-render after changing alignment
			case 'm':
				// Toggle layout mode
				currentLayout := r.Layout()
				if currentLayout == reader.LayoutPaged {
					r.SetLayout(reader.LayoutScrolled)
				} else {
					r.SetLayout(reader.LayoutPaged)
				}
				r.RenderPage(os.Stdout) // Re-render after changing layout
			case 'x':
				// Toggle animations
				fmt.Print("\nToggle animations (on/off): ")
				animStr, _ := inputReader.ReadString('\n')
				animStr = strings.TrimSpace(strings.ToLower(animStr))
				if animStr == "off" || animStr == "false" || animStr == "0" {
					r.SetAnimationEnabled(false)
					fmt.Println("Animations disabled")
				} else if animStr == "on" || animStr == "true" || animStr == "1" {
					r.SetAnimationEnabled(true)
					fmt.Println("Animations enabled")
				}
				fmt.Print("Press Enter to continue...")
				inputReader.ReadString('\n')
			case 't':
				// Change animation type
				fmt.Println("\nSelect animation type:")
				fmt.Println("1. Slide Left")
				fmt.Println("2. Slide Right")
				fmt.Println("3. Fade")
				fmt.Println("4. Typewriter")
				fmt.Println("5. Wipe")
				fmt.Println("6. Zoom")
				fmt.Print("Enter choice (1-6): ")
				choiceStr, _ := inputReader.ReadString('\n')
				choice, err := strconv.Atoi(strings.TrimSpace(choiceStr))
				if err == nil && choice >= 1 && choice <= 6 {
					animType := reader.AnimationType(choice - 1)
					r.SetAnimationType(animType)
					fmt.Printf("Animation type set to: %v\n", animType)
				} else {
					fmt.Println("Invalid choice")
				}
				fmt.Print("Press Enter to continue...")
				inputReader.ReadString('\n')
			}
		}
	},
}

// alignCmd represents the align command
var alignCmd = &cobra.Command{
	Use:   "align [left|justify]",
	Short: "Set text alignment",
	Long: `Set the text alignment for the EPUB reader.

Valid alignment options are:
  left     - Left-aligned text (default)
  justify  - Justified text`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		alignStr = args[0]
		if alignStr != "left" && alignStr != "justify" {
			return fmt.Errorf("invalid alignment: %s. Use 'left' or 'justify'", alignStr)
		}
		fmt.Printf("Text alignment set to: %s\n", alignStr)
		return nil
	},
}

// layoutCmd represents the layout command
var layoutCmd = &cobra.Command{
	Use:   "layout [paged|scrolled]",
	Short: "Set layout mode",
	Long: `Set the layout mode for the EPUB reader.

Valid layout modes are:
  paged    - View one page at a time (default)
  scrolled - Continuous scrolling`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		layoutStr = args[0]
		if layoutStr != "paged" && layoutStr != "scrolled" {
			return fmt.Errorf("invalid layout: %s. Use 'paged' or 'scrolled'", layoutStr)
		}
		fmt.Printf("Layout mode set to: %s\n", layoutStr)
		return nil
	},
}

func init() {
	rootCmd.AddCommand(readCmd)
	readCmd.AddCommand(alignCmd)
	readCmd.AddCommand(layoutCmd)

	// Add flags for read command
	readCmd.Flags().StringVarP(&fontFlag, "font", "f", "", "Use a custom font for display (use 'list' to see available fonts)")
	readCmd.Flags().BoolP("list-fonts", "l", false, "List available fonts and exit")
	readCmd.Flags().StringVarP(&alignStr, "align", "a", "left", "Text alignment (left, justify)")
	readCmd.Flags().StringVarP(&layoutStr, "layout", "m", "paged", "Layout mode (paged, scrolled)")

	// Add flags for align command
	alignCmd.Flags().StringVarP(&alignStr, "align", "a", "left", "Text alignment (left, justify)")

	// Add flags for layout command
	layoutCmd.Flags().StringVarP(&layoutStr, "layout", "m", "paged", "Layout mode (paged, scrolled)")
}
