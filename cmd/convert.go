package cmd

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"github.com/yourusername/epubcraft/internal/converter"
)

// convertCmd represents the convert command
var convertCmd = &cobra.Command{
	Use:   "convert [pdf file] [output.epub]",
	Short: "Convert a PDF file to EPUB format",
	Long: `Convert a PDF file to EPUB format while preserving the document structure.
The output file will be created in the current directory if no path is specified.`,
	Args:  cobra.RangeArgs(1, 2),
	Run: func(cmd *cobra.Command, args []string) {
		pdfFile := args[0]
		var epubFile string

		if len(args) == 2 {
			epubFile = args[1]
		} else {
			// Use the same filename but with .epub extension
			ext := filepath.Ext(pdfFile)
			baseName := strings.TrimSuffix(filepath.Base(pdfFile), ext)
			epubFile = baseName + ".epub"
		}

		// Get flags
		title, _ := cmd.Flags().GetString("title")
		author, _ := cmd.Flags().GetString("author")

		// Use the PDF filename as the title if not provided
		if title == "" {
			title = strings.TrimSuffix(filepath.Base(pdfFile), filepath.Ext(pdfFile))
		}

		// Initialize spinner
		globalSpinner.Prefix = "Converting "
		globalSpinner.FinalMSG = color.GreenString("✓ Conversion complete!\n")
		globalSpinner.Start()

		// Create a channel to track progress
		progressChan := make(chan int, 1)
		done := make(chan bool)

		// Start a goroutine to update the spinner with progress
		go func() {
			var totalPages int
			for {
				select {
				case pages := <-progressChan:
					totalPages = pages
				case currentPage := <-progressChan:
					globalSpinner.Suffix = fmt.Sprintf(" Page %d/%d", currentPage, totalPages)
				case <-done:
					return
				}
			}
		}()

		// Start timing the conversion
		startTime := time.Now()

		// Convert the PDF to EPUB
		err := converter.ConvertPDFToEPUB(
			pdfFile,
			epubFile,
			title,
			author,
			func(current, total int) {
				if current == 1 {
					progressChan <- total // Send total pages first
				}
				progressChan <- current // Then send current page
			},
		)

		// Signal the progress goroutine to stop
		close(done)

		// Stop the spinner
		globalSpinner.Stop()

		// Handle any errors
		if err != nil {
			color.Red("Error converting PDF to EPUB: %v\n", err)
			os.Exit(1)
		}

		// Calculate and display conversion time
		duration := time.Since(startTime).Round(time.Millisecond)
		absPath, _ := filepath.Abs(epubFile)
		
		fmt.Printf("\n%s %s\n", color.GreenString("✓"), color.BlueString("Success!"))
		fmt.Printf("  Input:  %s\n", pdfFile)
		fmt.Printf("  Output: %s\n", absPath)
		fmt.Printf("  Time:   %s\n\n", duration)
	},
}

func init() {
	rootCmd.AddCommand(convertCmd)
	// Add flags for convert command
	convertCmd.Flags().StringP("title", "t", "", "Set the title of the output EPUB")
	convertCmd.Flags().StringP("author", "a", "", "Set the author of the output EPUB")
}
