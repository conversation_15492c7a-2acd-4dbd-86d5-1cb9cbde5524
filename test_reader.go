package main

import (
	"fmt"
	"os"

	"github.com/yourusername/epubcraft/internal/reader"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run test_reader.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB reader with file: %s\n", epubFile)

	// Create reader
	r, err := reader.NewEPUBReader(epubFile, "", reader.AlignLeft, reader.LayoutPaged)
	if err != nil {
		fmt.Printf("Error creating reader: %v\n", err)
		os.Exit(1)
	}

	// Get page info
	page, total := r.PageInfo()
	fmt.Printf("Successfully loaded EPUB with %d pages\n", total)
	fmt.Printf("Current page: %d\n", page)

	// Get current page content (first 500 characters)
	content := r.CurrentPage()
	if len(content) > 500 {
		content = content[:500] + "..."
	}
	fmt.Printf("First page content:\n%s\n", content)

	// Test navigation
	fmt.Println("\nTesting navigation...")
	if r.NextPage() {
		page, _ = r.PageInfo()
		fmt.Printf("Successfully moved to page %d\n", page)
		
		content = r.CurrentPage()
		if len(content) > 200 {
			content = content[:200] + "..."
		}
		fmt.Printf("Second page content:\n%s\n", content)
	}

	if r.PrevPage() {
		page, _ = r.PageInfo()
		fmt.Printf("Successfully moved back to page %d\n", page)
	}

	fmt.Println("\nEPUB reader test completed successfully!")
}
