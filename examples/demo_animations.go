package main

import (
	"fmt"
	"os"
	"time"

	"github.com/yourusername/epubcraft/internal/reader"
)

func main() {
	fmt.Println("EPUBCraft Animation Demo")
	fmt.Println("========================")
	
	// Create animation manager
	animManager := reader.NewAnimationManager()
	
	// Demo content
	oldContent := []string{
		"Chapter 1: Introduction",
		"",
		"This is the first page of our book.",
		"It contains some sample text to demonstrate",
		"the animation system in EPUBCraft.",
		"",
		"The old content will slide away as we",
		"transition to the next page.",
	}
	
	newContent := []string{
		"Chapter 2: Getting Started",
		"",
		"Welcome to the second page!",
		"This page demonstrates smooth transitions",
		"between different content sections.",
		"",
		"The animation system supports multiple",
		"transition types including slides, fades,",
		"typewriter effects, and more.",
	}
	
	// Demo different animation types
	animations := []struct {
		name string
		animType reader.AnimationType
	}{
		{"Slide Left", reader.AnimationSlideLeft},
		{"Slide Right", reader.AnimationSlideRight},
		{"Fade", reader.AnimationFade},
		{"Typewriter", reader.AnimationTypewriter},
		{"Wipe", reader.AnimationWipe},
		{"Zoom", reader.AnimationZoom},
	}
	
	for _, anim := range animations {
		fmt.Printf("\n\nDemonstrating: %s\n", anim.name)
		fmt.Println("Press Enter to continue...")
		fmt.Scanln()
		
		// Set animation type
		animManager.SetAnimationType(anim.animType)
		animManager.SetDuration(800 * time.Millisecond)
		
		// Perform animation
		animManager.AnimatePageTransition(os.Stdout, oldContent, newContent, 1)
		
		time.Sleep(1 * time.Second)
	}
	
	fmt.Println("\n\nAnimation demo complete!")
	fmt.Println("To use animations in EPUBCraft:")
	fmt.Println("1. Run: ./bin/epubcraft read your-book.epub")
	fmt.Println("2. Press 'x' to toggle animations on/off")
	fmt.Println("3. Press 't' to change animation types")
	fmt.Println("4. Use 'n' and 'p' to navigate with animations")
}
