package main

import (
	"fmt"
	"os"
	"time"

	"github.com/taylorskalyo/goreader/epub"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run debug_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB file: %s\n", epubFile)

	start := time.Now()
	
	// Try to open the EPUB file
	fmt.Println("Opening EPUB file...")
	rc, err := epub.OpenReader(epubFile)
	if err != nil {
		fmt.Printf("Error opening EPUB file: %v\n", err)
		os.Exit(1)
	}
	defer rc.Close()

	fmt.Printf("✓ EPUB opened successfully in %v\n", time.Since(start))

	if len(rc.Rootfiles) == 0 {
		fmt.Println("✗ No rootfiles found")
		os.Exit(1)
	}

	pkg := &rc.Rootfiles[0].Package
	fmt.Printf("✓ Found %d spine items\n", len(pkg.Spine.Itemrefs))

	// Try to read first few items
	itemCount := 0
	for _, itemref := range pkg.Spine.Itemrefs {
		if itemCount >= 3 { // Only test first 3 items
			break
		}
		
		// Find the item in the manifest
		for _, item := range pkg.Manifest.Items {
			if item.ID == itemref.IDREF {
				fmt.Printf("Processing item %d: %s (%s)\n", itemCount+1, item.ID, item.MediaType)
				
				start := time.Now()
				r, err := item.Open()
				if err != nil {
					fmt.Printf("  ✗ Failed to open: %v\n", err)
					continue
				}

				content := make([]byte, 1024) // Read only first 1KB
				n, err := r.Read(content)
				r.Close()
				
				if err != nil && err.Error() != "EOF" {
					fmt.Printf("  ✗ Failed to read: %v\n", err)
					continue
				}

				fmt.Printf("  ✓ Read %d bytes in %v\n", n, time.Since(start))
				fmt.Printf("  Content preview: %.100s...\n", string(content[:min(n, 100)]))
				
				itemCount++
				break
			}
		}
	}

	fmt.Printf("✓ Successfully processed %d items\n", itemCount)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
