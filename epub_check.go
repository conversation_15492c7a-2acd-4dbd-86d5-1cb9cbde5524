package main

import (
	"fmt"
	"os"

	"github.com/taylorskalyo/goreader/epub"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run simple_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB file: %s\n", epubFile)

	// Try to open the EPUB file
	rc, err := epub.OpenReader(epubFile)
	if err != nil {
		fmt.Printf("Error opening EPUB file: %v\n", err)
		os.Exit(1)
	}
	defer rc.Close()

	fmt.Printf("Successfully opened EPUB file\n")
	fmt.Printf("Number of rootfiles: %d\n", len(rc.Rootfiles))

	if len(rc.Rootfiles) > 0 {
		pkg := &rc.Rootfiles[0].Package
		fmt.Printf("Spine items: %d\n", len(pkg.Spine.Itemrefs))
		fmt.Printf("Manifest items: %d\n", len(pkg.Manifest.Items))
	}

	fmt.Println("EPUB file is valid and readable!")
}
