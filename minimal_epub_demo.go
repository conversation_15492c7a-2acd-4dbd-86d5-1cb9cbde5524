package main

import (
	"fmt"
	"os"
	"time"

	"github.com/yourusername/epubcraft/internal/reader"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run minimal_epub_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing minimal EPUB reader with: %s\n", epubFile)

	start := time.Now()
	
	// Try to open the EPUB file with our simple reader
	fmt.Println("Opening EPUB file...")
	epub, err := reader.NewSimpleEPUB(epubFile)
	if err != nil {
		fmt.Printf("Error opening EPUB file: %v\n", err)
		os.Exit(1)
	}
	defer epub.Close()

	fmt.Printf("✓ EPUB opened successfully in %v\n", time.Since(start))

	// Get metadata
	fmt.Printf("✓ Title: %s\n", epub.GetTitle())
	fmt.Printf("✓ Author: %s\n", epub.GetAuthor())
	fmt.Printf("✓ Spine items: %d\n", epub.GetSpineCount())

	// Test reading first spine item (raw HTML)
	if epub.GetSpineCount() > 0 {
		fmt.Println("Reading first spine item (raw HTML)...")
		
		start := time.Now()
		content, err := epub.GetSpineItem(0)
		if err != nil {
			fmt.Printf("  ✗ Failed to read spine item: %v\n", err)
		} else {
			fmt.Printf("  ✓ Read %d bytes in %v\n", len(content), time.Since(start))
			
			// Show a preview of the raw HTML content
			preview := content
			if len(preview) > 500 {
				preview = preview[:500] + "..."
			}
			fmt.Printf("  Raw HTML preview:\n%s\n", preview)
		}
	}

	fmt.Println("✓ Minimal EPUB reader test completed successfully!")
}
