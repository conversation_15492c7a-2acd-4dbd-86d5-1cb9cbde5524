package main

import (
	"fmt"
	"os"
	"time"

	"github.com/pirmd/epub"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run alternative_test.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing EPUB file with pirmd/epub library: %s\n", epubFile)

	start := time.Now()
	
	// Try to open the EPUB file
	fmt.Println("Opening EPUB file...")
	book, err := epub.Open(epubFile)
	if err != nil {
		fmt.Printf("Error opening EPUB file: %v\n", err)
		os.Exit(1)
	}
	defer book.Close()

	fmt.Printf("✓ EPUB opened successfully in %v\n", time.Since(start))

	// Get metadata
	metadata := book.Metadata()
	fmt.Printf("✓ Title: %s\n", metadata.Title)
	fmt.Printf("✓ Author: %s\n", metadata.Creator)

	// Get spine (reading order)
	spine := book.Spine()
	fmt.Printf("✓ Found %d spine items\n", len(spine))

	// Try to read first few items
	for i, item := range spine {
		if i >= 3 { // Only test first 3 items
			break
		}
		
		fmt.Printf("Processing item %d: %s\n", i+1, item.HREF)
		
		start := time.Now()
		content, err := book.ReadFile(item.HREF)
		if err != nil {
			fmt.Printf("  ✗ Failed to read: %v\n", err)
			continue
		}

		fmt.Printf("  ✓ Read %d bytes in %v\n", len(content), time.Since(start))
		fmt.Printf("  Content preview: %.100s...\n", string(content[:min(len(content), 100)]))
	}

	fmt.Println("✓ Alternative EPUB library test completed successfully!")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
