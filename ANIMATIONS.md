# EPUBCraft Animation System

EPUBCraft now includes a comprehensive animation system that enhances the reading experience with smooth transitions and visual effects.

## Features

### Page Transition Animations
- **Slide Left/Right**: Pages slide horizontally during navigation
- **Fade**: Smooth fade-out/fade-in transitions between pages
- **Typewriter**: Text appears character by character for dramatic effect
- **Wipe**: Content is revealed with a wipe effect (top-to-bottom or bottom-to-top)
- **Zoom**: Pages zoom in from a smaller size to full size

### Status Bar Animations
- **Animated Progress Bar**: Shows reading progress with smooth animations
- **Blinking Indicators**: Visual feedback for current position
- **Color-coded Status**: Different colors for different states

### Interactive Feedback
- **Button Press Effects**: Visual feedback when pressing keys
- **Loading Animations**: Smooth spinners and progress indicators
- **Text Reveal**: Progressive text display for enhanced readability

## Usage

### Basic Controls
```bash
# Start reading with animations enabled (default)
./bin/epubcraft read your-book.epub

# Navigate with animated transitions
n, space    # Next page (with animation)
p, b        # Previous page (with animation)
```

### Animation Controls
```bash
x           # Toggle animations on/off
t           # Change animation type
            # Options: Slide Left, Slide Right, Fade, <PERSON><PERSON>, Wipe, Zoom
```

### Animation Types

#### 1. Slide Left (Default)
- Pages slide from right to left when going forward
- Pages slide from left to right when going backward
- Smooth horizontal movement with configurable speed

#### 2. Slide Right
- Reverse of Slide Left
- Pages slide from left to right when going forward
- Pages slide from right to left when going backward

#### 3. Fade
- Old page fades out gradually
- New page fades in gradually
- Smooth opacity transitions

#### 4. Typewriter
- Text appears character by character
- Includes blinking cursor effect
- Variable speed based on character type (punctuation slower, spaces faster)

#### 5. Wipe
- Content is revealed with a wipe effect
- Direction changes based on navigation (forward/backward)
- Smooth line-by-line revelation

#### 6. Zoom
- Pages start small and zoom to full size
- Centered scaling effect
- Smooth size transitions

## Configuration

### Animation Settings
- **Duration**: Configurable animation speed (default: 300ms)
- **Enable/Disable**: Toggle animations on/off
- **Type Selection**: Choose from 6 different animation types

### Performance
- Animations are optimized for terminal performance
- Minimal CPU usage during transitions
- Graceful fallback to static display if needed

## Technical Details

### Animation System Architecture
```
AnimationManager
├── Page Transition Animations
├── Progress Animations  
├── Status Bar Animations
└── Text Reveal Animations
```

### Key Components
- **AnimationManager**: Core animation engine
- **ProgressAnimator**: Handles progress bars and loading indicators
- **StatusBarAnimator**: Manages animated status displays
- **TextAnimator**: Controls text reveal effects

### Terminal Compatibility
- Works with most modern terminal emulators
- Uses ANSI escape sequences for positioning and effects
- Automatic fallback for unsupported terminals

## Examples

### Basic Reading Session
```bash
# Start reading
./bin/epubcraft read book.epub

# Navigate with default slide animation
n  # Next page (slides left)
p  # Previous page (slides right)

# Change to typewriter effect
t  # Select option 4 (Typewriter)
n  # Next page appears with typewriter effect

# Disable animations for faster navigation
x  # Toggle off
n  # Next page appears instantly
```

### Customizing Experience
```bash
# Try different animation types
t  # Change animation type
   # 1. Slide Left
   # 2. Slide Right  
   # 3. Fade
   # 4. Typewriter
   # 5. Wipe
   # 6. Zoom

# Toggle animations during reading
x  # Turn off for speed
x  # Turn back on for visual appeal
```

## Benefits

### Enhanced Reading Experience
- **Visual Appeal**: Smooth transitions make reading more engaging
- **Context Preservation**: Animations help maintain reading flow
- **Feedback**: Clear visual indication of navigation actions

### Accessibility
- **Configurable Speed**: Adjust animation duration for comfort
- **Disable Option**: Turn off animations if they cause distraction
- **Fallback Support**: Works even on basic terminals

### Performance
- **Optimized Rendering**: Efficient terminal updates
- **Minimal Resource Usage**: Low CPU and memory impact
- **Responsive Controls**: Animations don't block user input

## Troubleshooting

### Common Issues
1. **Animations appear choppy**
   - Try reducing animation duration
   - Check terminal performance
   - Consider disabling animations

2. **Text appears garbled during animations**
   - Ensure terminal supports ANSI escape sequences
   - Try a different animation type
   - Disable animations as fallback

3. **Slow performance**
   - Reduce animation duration
   - Use simpler animation types (Slide vs Typewriter)
   - Disable animations for faster navigation

### Terminal Compatibility
- **Recommended**: Modern terminals (iTerm2, Terminal.app, GNOME Terminal, etc.)
- **Supported**: Most terminals with ANSI support
- **Fallback**: Basic terminals (animations disabled automatically)

## Future Enhancements

### Planned Features
- **Custom Animation Curves**: Easing functions for smoother motion
- **Sound Effects**: Optional audio feedback (if terminal supports)
- **Theme-based Animations**: Different animation sets for different themes
- **Gesture Support**: Touch/swipe animations for supported terminals

### Performance Improvements
- **GPU Acceleration**: Hardware-accelerated rendering where available
- **Adaptive Quality**: Automatic quality adjustment based on performance
- **Caching**: Pre-rendered animation frames for smoother playback
