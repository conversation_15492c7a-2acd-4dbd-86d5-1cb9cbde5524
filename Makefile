# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
BINARY_NAME=epubcraft
BUILD_DIR=.
BIN_DIR=$(BUILD_DIR)/bin
BINARY_PATH=$(BIN_DIR)/$(BINARY_NAME)

# Phony targets
.PHONY: all build clean deps test install uninstall fmt vet lint help

# Default target
all: build

## Build the binary
build: $(BIN_DIR)
	cd cmd/epubcraft && $(GOBUILD) -o ../../$(BINARY_PATH) .

# Ensure bin directory exists
$(BIN_DIR):
	@mkdir -p $(BIN_DIR)

## Install dependencies
deps:
	$(GOGET) -v ./...

## Run tests
test:
	$(GOTEST) -v ./...

## Run tests with coverage
test-cover:
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

## Clean build artifacts
clean:
	$(GOCLEAN)
	rm -f coverage.out coverage.html
	rm -f $(BIN_DIR)/$(BINARY_NAME) 2>/dev/null || true

## Format code
fmt:
	$(GOCMD) fmt ./...

## Run go vet
govet:
	$(GOCMD) vet ./...


## Install the binary
install: build
	@echo "Installing..."
	@cp $(BINARY_PATH) $(shell go env GOPATH)/bin/$(BINARY_NAME)

## Uninstall the binary
uninstall:
	@echo "Uninstalling..."
	@rm -f $(shell go env GOPATH)/bin/$(BINARY_NAME)

## Show help
help:
	@echo 'Usage:'
	@echo '  make <target>'
	@echo ''
	@echo 'Targets:'
	@echo '  all          - Build the project (default)'
	@echo '  build        - Build the binary'
	@echo '  clean        - Remove build artifacts'
	@echo '  deps         - Download dependencies'
	@echo '  test         - Run tests'
	@echo '  test-cover   - Run tests with coverage report'
	@echo '  fmt          - Format source code'
	@echo '  govet        - Run go vet'
	@echo '  install      - Install the binary'
	@echo '  uninstall    - Uninstall the binary'
	@echo '  help         - Show this help message'

# Default target
.DEFAULT_GOAL := all
