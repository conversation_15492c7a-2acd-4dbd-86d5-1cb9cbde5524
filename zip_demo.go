package main

import (
	"archive/zip"
	"fmt"
	"os"
	"time"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run zip_demo.go <epub-file>")
		os.Exit(1)
	}

	epubFile := os.Args[1]
	fmt.Printf("Testing ZIP reading with: %s\n", epubFile)

	start := time.Now()
	
	// Try to open the EPUB file as a ZIP archive
	fmt.Println("Opening ZIP file...")
	zipReader, err := zip.OpenReader(epubFile)
	if err != nil {
		fmt.Printf("Error opening ZIP file: %v\n", err)
		os.Exit(1)
	}
	defer zipReader.Close()

	fmt.Printf("✓ ZIP opened successfully in %v\n", time.Since(start))
	fmt.Printf("✓ Found %d files in ZIP\n", len(zipReader.File))

	// List first 10 files
	fmt.Println("First 10 files:")
	for i, file := range zipReader.File {
		if i >= 10 {
			break
		}
		fmt.Printf("  %d. %s (%d bytes)\n", i+1, file.Name, file.UncompressedSize64)
	}

	fmt.Println("✓ ZIP reading test completed successfully!")
}
