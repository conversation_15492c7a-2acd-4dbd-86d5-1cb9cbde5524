<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html><html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml"><head><title>nav</title></head><body data-type="book"><nav data-type="toc" epub:type="toc" id="id30"><ol><li data-type="preface"><a href="preface01.html#id31">Brief Table of Contents (Not Yet Final)</a></li><li data-type="chapter"><a href="ch01.html#chapter_one">1. Hello, world!</a><ol>













<li data-type="sect1"><a href="ch01.html#id38">Introducing C++</a></li>






<li data-type="sect1"><a href="ch01.html#id39">Installing tools</a><ol>



















<li data-type="sect2"><a href="ch01.html#id40">Linux and MacOS</a></li>








<li data-type="sect2"><a href="ch01.html#id41">Windows</a></li>
</ol></li>






<li data-type="sect1"><a href="ch01.html#chapter_1_build_instructions">Using your tools</a><ol>


















































































<li data-type="sect2"><a href="ch01.html#id46">Running your program</a></li>
</ol></li>






<li data-type="sect1"><a href="ch01.html#id47">Writing to the screen</a><ol>















<li data-type="sect2"><a href="ch01.html#id48">Using println</a></li>








<li data-type="sect2"><a href="ch01.html#chp_01_troubleshooting">Troubleshooting</a></li>








<li data-type="sect2"><a href="ch01.html#id50">Using cout</a></li>
</ol></li>






<li data-type="sect1"><a href="ch01.html#chap01_in_depth">Understanding println and cout in depth</a></li>






<li data-type="sect1"><a href="ch01.html#id52">Conclusion</a></li>
</ol></li><li data-type="chapter"><a href="ch02.html#chapter_two">2. Variables and Keyboard Input</a><ol>















<li data-type="sect1"><a href="ch02.html#chap_2_variables">Declaring variables</a></li>






<li data-type="sect1"><a href="ch02.html#id54">Character input</a></li>






<li data-type="sect1"><a href="ch02.html#id55">Detecting input problems</a><ol>











<li data-type="sect2"><a href="ch02.html#id56">Input of real numbers</a></li>








<li data-type="sect2"><a href="ch02.html#id57">Detecting problems with numbers</a></li>








<li data-type="sect2"><a href="ch02.html#id58">Detecting more general problems</a></li>
</ol></li>






<li data-type="sect1"><a href="ch02.html#id59">A function for input with some tests</a><ol>













<li data-type="sect2"><a href="ch02.html#failing_test">Starting with a failing test</a></li>








<li data-type="sect2"><a href="ch02.html#id61">Breaking your code into functions</a></li>








<li data-type="sect2"><a href="ch02.html#id62">Starting with a failing test, again</a></li>








<li data-type="sect2"><a href="ch02.html#id63">Making the test pass</a></li>








<li data-type="sect2"><a href="ch02.html#id64">Refactor</a></li>








<li data-type="sect2"><a href="ch02.html#id65">Calling your new function from main</a></li>
</ol></li>






<li data-type="sect1"><a href="ch02.html#id66">Understanding cin and functions in more depth</a><ol>













<li data-type="sect2"><a href="ch02.html#chap_2_clearing_input_errors">Clearing input errors</a></li>








<li data-type="sect2"><a href="ch02.html#id68">More on Functions</a></li>
</ol></li>






<li data-type="sect1"><a href="ch02.html#id69">Conclusion</a></li>
</ol></li><li data-type="chapter"><a href="ch03.html#chapter_three">3. Exceptions and Expectations</a><ol>












<li data-type="sect1"><a href="ch03.html#id70">Exceptions</a><ol>











































<li data-type="sect2"><a href="ch03.html#id71">Throwing exceptions</a></li>








<li data-type="sect2"><a href="ch03.html#id72">Trying and catching</a></li>








<li data-type="sect2"><a href="ch03.html#id73">Handling exceptions with a try/catch block</a></li>
</ol></li>






<li data-type="sect1"><a href="ch03.html#id74">Expectations</a></li>






<li data-type="sect1"><a href="ch03.html#id75">Understanding Exceptions and Expectations in more Depth</a><ol>













<li data-type="sect2"><a href="ch03.html#id76">Other exception types</a></li>








<li data-type="sect2"><a href="ch03.html#id77">Position of catch blocks</a></li>








<li data-type="sect2"><a href="ch03.html#id78">Expected without a value</a></li>
</ol></li>






<li data-type="sect1"><a href="ch03.html#id79">Conclusion</a></li>
</ol></li><li data-type="chapter"><a href="ch04.html#chapter_four">4. Using loops, a std::array and a std::vector</a><ol>















<li data-type="sect1"><a href="ch04.html#id80">Input of several numbers using a loop</a><ol>














<li data-type="sect2"><a href="ch04.html#id81">A while loop</a></li>
</ol></li>






<li data-type="sect1"><a href="ch04.html#id82">Using an array</a><ol>


















































































<li data-type="sect2"><a href="ch04.html#id83">Displaying and using the numbers</a></li>
</ol></li>






<li data-type="sect1"><a href="ch04.html#id84">Using a vector</a><ol>


























<li data-type="sect2"><a href="ch04.html#id85">Adding more elements to a vector</a></li>








<li data-type="sect2"><a href="ch04.html#id86">A few other container functions</a></li>








<li data-type="sect2"><a href="ch04.html#id87">Getting several numbers in a vector</a></li>
</ol></li>






<li data-type="sect1"><a href="ch04.html#understanding_seq_containers">Understanding sequential containers in more depth</a><ol>











<li data-type="sect2"><a href="ch04.html#id89">Initialising containers with an initializer list</a></li>








<li data-type="sect2"><a href="ch04.html#add_to_a_vector">What happens when you add to a vector</a></li>








<li data-type="sect2"><a href="ch04.html#Chap4_erase_from_vector">What happens when you delete from a vector</a></li>








<li data-type="sect2"><a href="ch04.html#initialize_brace_or_bracket">Initialising a vector with a fixed value</a></li>








<li data-type="sect2"><a href="ch04.html#id93">Other sequential containers</a></li>
</ol></li>






<li data-type="sect1"><a href="ch04.html#id94">Conclusion</a></li>
</ol></li><li data-type="chapter"><a href="ch05.html#chapter_five">5. Using Standard Library Algorithms</a><ol>













<li data-type="sect1"><a href="ch05.html#id95">Getting several numbers into a vector (again)</a></li>






<li data-type="sect1"><a href="ch05.html#Chap5_Analysis">Analyzing your numbers using algorithms</a><ol>





























<li data-type="sect2"><a href="ch05.html#id97">Using predicates in algorithms</a></li>








<li data-type="sect2"><a href="ch05.html#iterators">Using iterators in algorithms</a></li>








<li data-type="sect2"><a href="ch05.html#id99">The old way to remove items</a></li>








<li data-type="sect2"><a href="ch05.html#id100">Finding an average with an algorithm</a></li>
</ol></li>






<li data-type="sect1"><a href="ch05.html#id101">Understanding Algorithms in More Depth</a><ol>











<li data-type="sect2"><a href="ch05.html#id102">Using for loops</a></li>








<li data-type="sect2"><a href="ch05.html#id103">Binary Operators and Predicates</a></li>








<li data-type="sect2"><a href="ch05.html#more_on_iterators">More on iterators</a></li>
</ol></li>






<li data-type="sect1"><a href="ch05.html#id105">Conclusion</a></li>
</ol></li><li data-type="chapter"><a href="ch06.html#chapter_six">6. Lambdas and the Ranges Library</a><ol>















<li data-type="sect1"><a href="ch06.html#id106">Removing negative numbers using a lambda</a><ol>




















































<li data-type="sect2"><a href="ch06.html#id107">Using a lambda to vary behavior via std::function</a></li>
</ol></li>






<li data-type="sect1"><a href="ch06.html#id108">Filtering out negative numbers using the ranges’ view</a></li>






<li data-type="sect1"><a href="ch06.html#fun_and_profit">Using lambda captures for fun and profit</a></li>






<li data-type="sect1"><a href="ch06.html#chap6_in_depth">Understanding Lambdas and Views in More Depth</a><ol>











<li data-type="sect2"><a href="ch06.html#id111">Lambda captures by value</a></li>








<li data-type="sect2"><a href="ch06.html#id112">Lambda captures by reference</a></li>








<li data-type="sect2"><a href="ch06.html#id113">Composing views</a></li>








<li data-type="sect2"><a href="ch06.html#id114">Lazy views</a></li>
</ol></li>






<li data-type="sect1"><a href="ch06.html#id115">Conclusion</a></li>
</ol></li></ol></nav><div id="sec-overlay" style="display:none;"><div id="sec-container"></div></div></body></html>
