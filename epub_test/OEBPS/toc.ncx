<?xml version="1.0" encoding="UTF-8"?>
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1"><head><meta name="cover" content="cover"/><meta name="dtb:uid" content="9781098178147"/></head><docTitle><text>Introducing C++</text></docTitle><navMap><navPoint id="preface-id31" playOrder="1"><navLabel><text>Brief Table of Contents (Not Yet Final)</text></navLabel><content src="preface01.html#id31"/></navPoint><navPoint id="chapter-id32" playOrder="2"><navLabel><text>1. Hello, world!</text></navLabel><content src="ch01.html#chapter_one"/><navPoint id="sect1-id38" playOrder="3"><navLabel><text>Introducing C++</text></navLabel><content src="ch01.html#id38"/></navPoint><navPoint id="sect1-id39" playOrder="4"><navLabel><text>Installing tools</text></navLabel><content src="ch01.html#id39"/><navPoint id="sect2-id40" playOrder="5"><navLabel><text>Linux and MacOS</text></navLabel><content src="ch01.html#id40"/></navPoint><navPoint id="sect2-id41" playOrder="6"><navLabel><text>Windows</text></navLabel><content src="ch01.html#id41"/></navPoint></navPoint><navPoint id="sect1-id42" playOrder="7"><navLabel><text>Using your tools</text></navLabel><content src="ch01.html#chapter_1_build_instructions"/><navPoint id="sect3-id43" playOrder="8"><navLabel><text>GCC</text></navLabel><content src="ch01.html#id43"/></navPoint><navPoint id="sect3-id44" playOrder="9"><navLabel><text>Clang</text></navLabel><content src="ch01.html#id44"/></navPoint><navPoint id="sect3-id45" playOrder="10"><navLabel><text>Windows</text></navLabel><content src="ch01.html#id45"/></navPoint><navPoint id="sect2-id46" playOrder="11"><navLabel><text>Running your program</text></navLabel><content src="ch01.html#id46"/></navPoint></navPoint><navPoint id="sect1-id47" playOrder="12"><navLabel><text>Writing to the screen</text></navLabel><content src="ch01.html#id47"/><navPoint id="sect2-id48" playOrder="13"><navLabel><text>Using println</text></navLabel><content src="ch01.html#id48"/></navPoint><navPoint id="sect2-id49" playOrder="14"><navLabel><text>Troubleshooting</text></navLabel><content src="ch01.html#chp_01_troubleshooting"/></navPoint><navPoint id="sect2-id50" playOrder="15"><navLabel><text>Using cout</text></navLabel><content src="ch01.html#id50"/></navPoint></navPoint><navPoint id="sect1-id51" playOrder="16"><navLabel><text>Understanding println and cout in depth</text></navLabel><content src="ch01.html#chap01_in_depth"/></navPoint><navPoint id="sect1-id52" playOrder="17"><navLabel><text>Conclusion</text></navLabel><content src="ch01.html#id52"/></navPoint></navPoint><navPoint id="chapter-id33" playOrder="18"><navLabel><text>2. Variables and Keyboard Input</text></navLabel><content src="ch02.html#chapter_two"/><navPoint id="sect1-id53" playOrder="19"><navLabel><text>Declaring variables</text></navLabel><content src="ch02.html#chap_2_variables"/></navPoint><navPoint id="sect1-id54" playOrder="20"><navLabel><text>Character input</text></navLabel><content src="ch02.html#id54"/></navPoint><navPoint id="sect1-id55" playOrder="21"><navLabel><text>Detecting input problems</text></navLabel><content src="ch02.html#id55"/><navPoint id="sect2-id56" playOrder="22"><navLabel><text>Input of real numbers</text></navLabel><content src="ch02.html#id56"/></navPoint><navPoint id="sect2-id57" playOrder="23"><navLabel><text>Detecting problems with numbers</text></navLabel><content src="ch02.html#id57"/></navPoint><navPoint id="sect2-id58" playOrder="24"><navLabel><text>Detecting more general problems</text></navLabel><content src="ch02.html#id58"/></navPoint></navPoint><navPoint id="sect1-id59" playOrder="25"><navLabel><text>A function for input with some tests</text></navLabel><content src="ch02.html#id59"/><navPoint id="sect2-id60" playOrder="26"><navLabel><text>Starting with a failing test</text></navLabel><content src="ch02.html#failing_test"/></navPoint><navPoint id="sect2-id61" playOrder="27"><navLabel><text>Breaking your code into functions</text></navLabel><content src="ch02.html#id61"/></navPoint><navPoint id="sect2-id62" playOrder="28"><navLabel><text>Starting with a failing test, again</text></navLabel><content src="ch02.html#id62"/></navPoint><navPoint id="sect2-id63" playOrder="29"><navLabel><text>Making the test pass</text></navLabel><content src="ch02.html#id63"/></navPoint><navPoint id="sect2-id64" playOrder="30"><navLabel><text>Refactor</text></navLabel><content src="ch02.html#id64"/></navPoint><navPoint id="sect2-id65" playOrder="31"><navLabel><text>Calling your new function from main</text></navLabel><content src="ch02.html#id65"/></navPoint></navPoint><navPoint id="sect1-id66" playOrder="32"><navLabel><text>Understanding cin and functions in more depth</text></navLabel><content src="ch02.html#id66"/><navPoint id="sect2-id67" playOrder="33"><navLabel><text>Clearing input errors</text></navLabel><content src="ch02.html#chap_2_clearing_input_errors"/></navPoint><navPoint id="sect2-id68" playOrder="34"><navLabel><text>More on Functions</text></navLabel><content src="ch02.html#id68"/></navPoint></navPoint><navPoint id="sect1-id69" playOrder="35"><navLabel><text>Conclusion</text></navLabel><content src="ch02.html#id69"/></navPoint></navPoint><navPoint id="chapter-id34" playOrder="36"><navLabel><text>3. Exceptions and Expectations</text></navLabel><content src="ch03.html#chapter_three"/><navPoint id="sect1-id70" playOrder="37"><navLabel><text>Exceptions</text></navLabel><content src="ch03.html#id70"/><navPoint id="sect2-id71" playOrder="38"><navLabel><text>Throwing exceptions</text></navLabel><content src="ch03.html#id71"/></navPoint><navPoint id="sect2-id72" playOrder="39"><navLabel><text>Trying and catching</text></navLabel><content src="ch03.html#id72"/></navPoint><navPoint id="sect2-id73" playOrder="40"><navLabel><text>Handling exceptions with a try/catch block</text></navLabel><content src="ch03.html#id73"/></navPoint></navPoint><navPoint id="sect1-id74" playOrder="41"><navLabel><text>Expectations</text></navLabel><content src="ch03.html#id74"/></navPoint><navPoint id="sect1-id75" playOrder="42"><navLabel><text>Understanding Exceptions and Expectations in more Depth</text></navLabel><content src="ch03.html#id75"/><navPoint id="sect2-id76" playOrder="43"><navLabel><text>Other exception types</text></navLabel><content src="ch03.html#id76"/></navPoint><navPoint id="sect2-id77" playOrder="44"><navLabel><text>Position of catch blocks</text></navLabel><content src="ch03.html#id77"/></navPoint><navPoint id="sect2-id78" playOrder="45"><navLabel><text>Expected without a value</text></navLabel><content src="ch03.html#id78"/></navPoint></navPoint><navPoint id="sect1-id79" playOrder="46"><navLabel><text>Conclusion</text></navLabel><content src="ch03.html#id79"/></navPoint></navPoint><navPoint id="chapter-id35" playOrder="47"><navLabel><text>4. Using loops, a std::array and a std::vector</text></navLabel><content src="ch04.html#chapter_four"/><navPoint id="sect1-id80" playOrder="48"><navLabel><text>Input of several numbers using a loop</text></navLabel><content src="ch04.html#id80"/><navPoint id="sect2-id81" playOrder="49"><navLabel><text>A while loop</text></navLabel><content src="ch04.html#id81"/></navPoint></navPoint><navPoint id="sect1-id82" playOrder="50"><navLabel><text>Using an array</text></navLabel><content src="ch04.html#id82"/><navPoint id="sect2-id83" playOrder="51"><navLabel><text>Displaying and using the numbers</text></navLabel><content src="ch04.html#id83"/></navPoint></navPoint><navPoint id="sect1-id84" playOrder="52"><navLabel><text>Using a vector</text></navLabel><content src="ch04.html#id84"/><navPoint id="sect2-id85" playOrder="53"><navLabel><text>Adding more elements to a vector</text></navLabel><content src="ch04.html#id85"/></navPoint><navPoint id="sect2-id86" playOrder="54"><navLabel><text>A few other container functions</text></navLabel><content src="ch04.html#id86"/></navPoint><navPoint id="sect2-id87" playOrder="55"><navLabel><text>Getting several numbers in a vector</text></navLabel><content src="ch04.html#id87"/></navPoint></navPoint><navPoint id="sect1-id88" playOrder="56"><navLabel><text>Understanding sequential containers in more depth</text></navLabel><content src="ch04.html#understanding_seq_containers"/><navPoint id="sect2-id89" playOrder="57"><navLabel><text>Initialising containers with an initializer list</text></navLabel><content src="ch04.html#id89"/></navPoint><navPoint id="sect2-id90" playOrder="58"><navLabel><text>What happens when you add to a vector</text></navLabel><content src="ch04.html#add_to_a_vector"/></navPoint><navPoint id="sect2-id91" playOrder="59"><navLabel><text>What happens when you delete from a vector</text></navLabel><content src="ch04.html#Chap4_erase_from_vector"/></navPoint><navPoint id="sect2-id92" playOrder="60"><navLabel><text>Initialising a vector with a fixed value</text></navLabel><content src="ch04.html#initialize_brace_or_bracket"/></navPoint><navPoint id="sect2-id93" playOrder="61"><navLabel><text>Other sequential containers</text></navLabel><content src="ch04.html#id93"/></navPoint></navPoint><navPoint id="sect1-id94" playOrder="62"><navLabel><text>Conclusion</text></navLabel><content src="ch04.html#id94"/></navPoint></navPoint><navPoint id="chapter-id36" playOrder="63"><navLabel><text>5. Using Standard Library Algorithms</text></navLabel><content src="ch05.html#chapter_five"/><navPoint id="sect1-id95" playOrder="64"><navLabel><text>Getting several numbers into a vector (again)</text></navLabel><content src="ch05.html#id95"/></navPoint><navPoint id="sect1-id96" playOrder="65"><navLabel><text>Analyzing your numbers using algorithms</text></navLabel><content src="ch05.html#Chap5_Analysis"/><navPoint id="sect2-id97" playOrder="66"><navLabel><text>Using predicates in algorithms</text></navLabel><content src="ch05.html#id97"/></navPoint><navPoint id="sect2-id98" playOrder="67"><navLabel><text>Using iterators in algorithms</text></navLabel><content src="ch05.html#iterators"/></navPoint><navPoint id="sect2-id99" playOrder="68"><navLabel><text>The old way to remove items</text></navLabel><content src="ch05.html#id99"/></navPoint><navPoint id="sect2-id100" playOrder="69"><navLabel><text>Finding an average with an algorithm</text></navLabel><content src="ch05.html#id100"/></navPoint></navPoint><navPoint id="sect1-id101" playOrder="70"><navLabel><text>Understanding Algorithms in More Depth</text></navLabel><content src="ch05.html#id101"/><navPoint id="sect2-id102" playOrder="71"><navLabel><text>Using for loops</text></navLabel><content src="ch05.html#id102"/></navPoint><navPoint id="sect2-id103" playOrder="72"><navLabel><text>Binary Operators and Predicates</text></navLabel><content src="ch05.html#id103"/></navPoint><navPoint id="sect2-id104" playOrder="73"><navLabel><text>More on iterators</text></navLabel><content src="ch05.html#more_on_iterators"/></navPoint></navPoint><navPoint id="sect1-id105" playOrder="74"><navLabel><text>Conclusion</text></navLabel><content src="ch05.html#id105"/></navPoint></navPoint><navPoint id="chapter-id37" playOrder="75"><navLabel><text>6. Lambdas and the Ranges Library</text></navLabel><content src="ch06.html#chapter_six"/><navPoint id="sect1-id106" playOrder="76"><navLabel><text>Removing negative numbers using a lambda</text></navLabel><content src="ch06.html#id106"/><navPoint id="sect2-id107" playOrder="77"><navLabel><text>Using a lambda to vary behavior via std::function</text></navLabel><content src="ch06.html#id107"/></navPoint></navPoint><navPoint id="sect1-id108" playOrder="78"><navLabel><text>Filtering out negative numbers using the ranges’ view</text></navLabel><content src="ch06.html#id108"/></navPoint><navPoint id="sect1-id109" playOrder="79"><navLabel><text>Using lambda captures for fun and profit</text></navLabel><content src="ch06.html#fun_and_profit"/></navPoint><navPoint id="sect1-id110" playOrder="80"><navLabel><text>Understanding Lambdas and Views in More Depth</text></navLabel><content src="ch06.html#chap6_in_depth"/><navPoint id="sect2-id111" playOrder="81"><navLabel><text>Lambda captures by value</text></navLabel><content src="ch06.html#id111"/></navPoint><navPoint id="sect2-id112" playOrder="82"><navLabel><text>Lambda captures by reference</text></navLabel><content src="ch06.html#id112"/></navPoint><navPoint id="sect2-id113" playOrder="83"><navLabel><text>Composing views</text></navLabel><content src="ch06.html#id113"/></navPoint><navPoint id="sect2-id114" playOrder="84"><navLabel><text>Lazy views</text></navLabel><content src="ch06.html#id114"/></navPoint></navPoint><navPoint id="sect1-id115" playOrder="85"><navLabel><text>Conclusion</text></navLabel><content src="ch06.html#id115"/></navPoint></navPoint></navMap></ncx>
