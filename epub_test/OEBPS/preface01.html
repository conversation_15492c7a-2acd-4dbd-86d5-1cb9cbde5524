<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="preface" epub:type="preface" data-pdf-bookmark="Brief Table of Contents (Not Yet Final)"><div class="preface" id="id31">
<h1>Brief Table of Contents (<em>Not Yet Final</em>)</h1>

<p>Chapter 1: Hello, world! (available)</p>

<p>Chapter 2: Variables and keyboard input (available)</p>

<p>Chapter 3: Exceptions and expectations (available)</p>

<p>Chapter 4: Using loops, a std::array, and a std::vector (available)</p>

<p>Chapter 5: Using standard library algorithms (available)</p>

<p>Chapter 6: Lambdas and the ranges library (available)</p>

<p><em>Chapter 7: Random numbers</em> (unavailable)</p>

<p><em>Chapter 8: Working with files</em> (unavailable)</p>

<p><em>Chapter 9: Strings and formatting</em> (unavailable)</p>

<p><em>Chapter 10: Classes and structures, part I</em> (unavailable)</p>

<p><em>Chapter 11: Classes and structures, part II</em> (unavailable)</p>

<p><em>Chapter 12: Classes and structures, part III</em> (unavailable)</p>

<p><em>Chapter 13: Runtime OOP</em> (unavailable)</p>

<p><em>Chapter 14: Enums and vocabulary types</em> (unavailable)</p>

<p><em>Chapter 15: Write your own template</em> (unavailable)</p>
<!-- italicize any chapter titles that are not yet available in ER --></div></section></div></div></body></html>