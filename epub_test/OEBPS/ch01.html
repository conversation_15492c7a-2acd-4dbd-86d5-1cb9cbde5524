<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 1. Hello, world!"><div class="chapter" id="chapter_one">
<h1><span class="label">Chapter 1. </span>Hello, world!</h1>

<aside data-type="sidebar" epub:type="sidebar"><div class="sidebar" id="id117">
<h1>A Note for Early Release Readers</h1>
<p>With Early Release ebooks, you get books in their earliest form—the author’s raw and unedited content as they write—so you can take advantage of these technologies long before the official release of these titles.</p>

<p>This will be the 1st chapter of the final book.</p>

<p>If you have comments about how we might improve the content and/or examples in this book, or if you notice missing material within this chapter, please reach out to the editor at <em><EMAIL></em>.</p>
</div></aside>

<p>In this chapter, we will walk through a very short code example to print a greeting on the screen. I will explain some background and syntax, giving you code to try. You will learn about the <code>main</code> program entry point and the basics of a function, and you’ll get your toolchain up and working.</p>

<p>In order to get the most from this book, try to run the code examples and play with them. By the end of this chapter, you will be able to build and run a small program that generates output, and you’ll know some basic C++ syntax. You will then be prepared to handle input in the next chapter.</p>






<section data-type="sect1" data-pdf-bookmark="Introducing C++"><div class="sect1" id="id38">
<h1>Introducing C++</h1>

<p>Humans write code, but computers only understand 0s and 1s, so our code needs to be “translated” for the computer. Some languages, like Python and JavaScript, are <em>interpreted</em>, meaning that the tools read the code and decide what to do <em>dynamically</em>, meaning at runtime. Every time such code is run, it must be reinterpreted.</p>

<p>Other languages, like Java and C#, <em>compile</em> to an <em>intermediate language</em>, for example bytecode for Java. The output is interpreted by a virtual machine, so you can compile your code once and run it almost anywhere. This is more efficient than interpreting code every time it is run, because the initial transformation step needs only happen once.</p>

<p>C++ is different, though it follows a process used by C, Fortran, and several other languages. C++ source code is transformed <em>directly</em> into something the computer understands. When you build C++ code, two steps happen.</p>

<p>First, a <em>compiler</em> reads your code and produces object files that are specific to your target machine, such as 32-bit Windows, 64-bit Linux, or a specific embedded system. If you want your code to run on a different machine, you need to rebuild it. For a small program, the object files produced by the compiler might stay in memory; for a larger program, you are likely to see them, often with the extensions *.o or *.obj, generated on your machine.</p>

<p>Second, the <em>linker</em> stitches the object files together to produce a library or program that you can run.</p>

<p>In short, you write code, and the compiler parses your code and generates object files, which the linker joins together, as shown in <a data-type="xref" href="#fig_1_1">Figure 1-1</a>.</p>

<figure><div id="fig_1_1" class="figure">
<img src="assets/fig_1_1_compile_link.png" alt="Compile and link steps" width="1280" height="720"/>
<h6><span class="label">Figure 1-1. </span>The compiler uses source files to generate object files, and the linker pieces these together to make the final output.</h6>
</div></figure>

<p>In theory, compiling and linking up front for a specific machine can make a program run quicker. In fact, C++ is often chosen for speed; your browser or Java virtual machine may be implemented in it.</p>

<p>C++ does have a reputation of being difficult. It is a relatively low-level language, which gives you more control and the potential to write very fast code. To draw an analogy, a car with a manual transmission gives the driver more control than an automatic. You can go faster, but if you don’t know what you’re doing, you might use the wrong gear or stall the engine.</p>

<p>Similarly, one small mistake in your C++ code could make the compiler spew forth many errors or the linker simply claim, “Error, function not found.” You might find you hit a problem once in a while as you work through this book. Don’t panic. You will build up an intuition of where to look for problems and I will guide you, starting simply.</p>

<p>If you already know another programming language, you will get more of a feel for what happens under the hood in that language as you read this book. C++ takes you closer to the hardware, giving you a deeper understanding of programming in any language. Taking time to learn C++ will pay off.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Installing tools"><div class="sect1" id="id39">
<h1>Installing tools</h1>

<p>You can use vim or emacs or another editor to type in your code. You then need to build your code. Alternatively, you can use an integrated development environment (IDE) to do both.</p>

<p>After you choose an editor, you need C++ tools to build your code. Your machine might already have some tools installed. People often refer to a “C++ compiler” when they really mean a compiler and a linker. If I slip into saying “compiler,” you’ll know I mean both.</p>

<p>Another useful tool is the <a href="https://godbolt.org/">Compiler Explorer</a>, which lets you try out code online. People often refer to the site as Godbolt, because it was created by Matt Godbolt. You can choose a compiler and build and run your code there. By default, you type your code in the left window, and the right-hand side shows the assembled output. The linker uses the assembled output to form the program.</p>

<figure><div id="fig_1_2" class="figure">
<img src="assets/fig_1_2.png" alt="Compiler Explorer" width="1156" height="393"/>
<h6><span class="label">Figure 1-2. </span>The default landing page on Compiler Explorer, showing code on the left and assembled output on the right.</h6>
</div></figure>

<p>Let’s see if you have C++ tools already, and install some if you don’t.</p>








<section data-type="sect2" data-pdf-bookmark="Linux and MacOS"><div class="sect2" id="id40">
<h2>Linux and MacOS</h2>

<p>Linux and MacOS tend to come with the <a href="https://gcc.gnu.org/">Gnu compiler collection</a> (GCC). GCC can build several languages, but you should use g++ to compile C++ code. See if you have g++ by opening a prompt and typing:</p>

<pre data-type="programlisting" data-code-language="bash">g++<code class="w"> </code>--version<code class="w"></code></pre>

<p>If you see a version number, you have a compiler. However, if the version number is not in double figures, it’s very old. Find your system’s instructions to install GCC or get a newer version, if needed.</p>

<p>Instead of GCC, you may have <a href="https://clang.llvm.org/">clang</a> installed.
Open a prompt and try:</p>

<pre data-type="programlisting" data-code-language="bash">clang++<code class="w"> </code>--version<code class="w"></code></pre>

<p>As with g++, clang can build a variety of languages, but we use clang++ for C++. Clang uses the <a href="https://llvm.org/">LLVM</a> compiler and toolchain. If you get a version number that is not in double figures, it’s also very old. Find your system’s instructions to install clang or get a newer version, if needed.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Windows"><div class="sect2" id="id41">
<h2>Windows</h2>

<p>For Windows, you also have a choice: you can try clang, GCC, or the Microsoft compiler.
<a href="https://code.visualstudio.com/docs/languages/cpp">Microsoft</a> provides step-by-step instructions for all three of these toolchains. Alternatively, you can get the community edition of <a href="https://visualstudio.microsoft.com/vs/community/">Visual Studio</a>, which provides the Visual Studio C++ tools and IDE.</p>

<p>A prompt needs to know the path to the tools, so you can either manually add this to your <code>PATH</code> environment variable, or open a Developer Command Prompt if you’ve installed Visual Studio. From a suitable prompt, type <code>cl</code> and see what it says:</p>

<pre data-type="programlisting" data-code-language="bash">&gt;<code class="w"> </code>cl<code class="w"></code>
Microsoft<code class="w"> </code><code class="o">(</code>R<code class="o">)</code><code class="w"> </code>C/C++<code class="w"> </code>Optimizing<code class="w"> </code>Compiler<code class="w"> </code>Version<code class="w"> </code><code class="m">19</code>.40.33811<code class="w"> </code><code class="k">for</code><code class="w"> </code>x86<code class="w"></code></pre>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Using your tools"><div class="sect1" id="chapter_1_build_instructions">
<h1>Using your tools</h1>

<p>Let’s make a program you can run. <a data-type="xref" href="#fig_1_1">Figure 1-1</a> shows several source files being built together, but you only need a single file to make your first program.</p>

<p>C++ programs are composed of <em>functions</em>, which group together statements into a block. Functions can call other functions. A function may or may not return a value. Functions use the keyword <code>void</code> to indicate that they don’t return anything.</p>

<p>When you write a function that returns a value, you must specify its type. All values in C++ have a type. For example, the basic numeric type is <code>int</code> (integer), which can represent both positive and negative numbers. The maximum and minimum value of an <code>int</code> depends on the target machine and compiler, but the C++ standard guarantees a range of at least -32,768 to 32,767. Most modern machines have a much larger range. Because <code>int</code> is a <a href="https://en.cppreference.com/w/cpp/language/types">fundamental type</a>, it is immediately available as part of the <em>core language</em>.</p>
<div data-type="tip"><h6>Tip</h6>
<p>C++ is standardized by the International Organization for Standardization (ISO). A working group, called WG21, agrees on new versions. The <a href="https://isocpp.org/std">ISOCpp</a> has details about the process. There are other working groups for other languages; however, many languages are not ISO-standardized.</p>
</div>

<p>A program or app requires a <code>main</code> function, which always returns an <code>int</code>, indicating success or failure.
Create an empty file, call it <em>empty.cpp</em>, and type in this very short program:</p>
<div id="first_main_code_block" data-type="example">
<h5><span class="label">Example 1-1. </span>Main C++ function</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_hello__world__CO1-1" href="#callout_hello__world__CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w"> </code><a class="co" id="co_hello__world__CO1-2" href="#callout_hello__world__CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code><code class="w"> </code><a class="co" id="co_hello__world__CO1-3" href="#callout_hello__world__CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_hello__world__CO1-1" href="#co_hello__world__CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Function head</p></dd>
<dt><a class="co" id="callout_hello__world__CO1-2" href="#co_hello__world__CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Function body</p></dd>
</dl></div>

<p>Almost any C++ function has a return type, a function name, and parentheses indicating any <em>parameters</em>, or values, sent into the function. Empty parentheses mean the function has no parameters.</p>

<p>There are a few different ways to introduce a function. However, let’s start simply. You need a return type, a name, and empty parentheses. This forms a <em>function head</em> or <em>signature</em>, which looks like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">()</code><code class="w"></code></pre>

<p>After the function head is a set of curly braces. The statements comprising the <em>function body</em> go in between these, but an empty function is fine too (though it won’t do much).</p>

<p>The function <code>main</code> is special. By default, it returns the <code>int</code> 0 to indicate no errors, so you don’t need to specify the return.</p>

<p>Save your file, and you’re ready to build your first C++ program. Open a command prompt and navigate to your <em>empty.cpp</em> file. Many IDEs have a <code>build</code> button, but working from the command line gives you an idea of what is happening.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>I am going to explain two very important flags for you to provide to the toolchain, and show you how to set them from a prompt. If you want to use an IDE instead, look at your IDE’s documentation.</p>
</div>

<p>The instructions vary slightly between compilers, but all have parts in common, following this pattern:</p>

<pre data-type="programlisting" data-code-language="bash">tool_name<code class="w"> </code><code class="o">[</code>optional<code class="w"> </code>flags<code class="o">]</code><code class="w"> </code>source_name.cpp<code class="w"> </code>-o<code class="w"> </code>output_name<code class="w"></code></pre>

<p>You state the tool (for example, clang), maybe use some flags, then state the source file or files. You can use <code>-o</code> to specify the output program’s name. Windows uses a different convention for specifying the output name, but will pick a sensible default name.</p>

<p>You are going to use two optional flags. First, you will ask for (almost) all warnings. Some tools spell this <code>-Wall</code>, but Windows uses <code>/Wall</code>: a <code>W</code> for warnings, and <code>all</code> for all.</p>

<p>Most languages have warnings. They indicate a potential problem, rather than a mistake. A warning is not an error, but it might be telling you something important.
In C++, you can turn on extra warnings, too. For now, using <code>Wall</code> is enough.</p>

<p>The second optional flag you will use states which version or standard of C++ you require.
For example, for C++23, you might use <code>-std=c++23</code>.
For Windows, use a slash instead of a dash and a colon instead of an equal sign <code>/std:c++23</code>.
For older versions of clang, use <code>2b</code> instead of <code>23</code> <code>-std=c++2b</code>.</p>

<p>Each compiler can target a specific range of language versions. A new version of C++ has been released every three years since 2011, and the number indicates the year. Each compiler version tends to implement a subset of the full standard, so if a feature may not work for you, I’ll let you know and suggest an alternative approach.</p>
<div data-type="tip"><h6>Tip</h6>
<p>The <a href="https://en.cppreference.com/w/cpp/compiler_support">CppReference</a> website gives a list of which compilers support which features. Have a look if you see errors telling you your compiler doesn’t believe a certain function exists, or similar. Remember to use the <code>std=</code> flag.</p>
</div>

<p>The instructions for building your source file into a program vary slightly between tools, so follow the appropriate subsection to build your code. If you have more than one compiler and can’t decide which to use, try them all.</p>










<section data-type="sect3" data-pdf-bookmark="GCC"><div class="sect3" id="id43">
<h3>GCC</h3>

<p>If you are using GCC, open a prompt and type:</p>

<pre data-type="programlisting" data-code-language="bash">g++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++23<code class="w"> </code>empty.cpp<code class="w"> </code>-o<code class="w"> </code>empty<code class="w"></code></pre>

<p>If you have more than one compiler version, you can specify a specific tool: for example, <code>g++-14</code> instead of <code>g++</code>.</p>
</div></section>










<section data-type="sect3" data-pdf-bookmark="Clang"><div class="sect3" id="id44">
<h3>Clang</h3>

<p>For clang, open a prompt and type:</p>

<pre data-type="programlisting" data-code-language="bash">clang++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++2b<code class="w"> </code>empty.cpp<code class="w"> </code>-o<code class="w"> </code>empty<code class="w"></code></pre>

<p>If you have more than one compiler version, you can specify a specific tool: for example, <code>clang++-15</code> instead of <code>clang++</code>.</p>
</div></section>










<section data-type="sect3" data-pdf-bookmark="Windows"><div class="sect3" id="id45">
<h3>Windows</h3>

<p>For Windows, you need the tools on your <code>path</code>. If you have a version of Visual Studio installed, the simplest way to do this is to open a developer command prompt from the Windows start menu.</p>

<p>Windows uses slashes instead of dashes, and we will use another optional flag, <code>EHSc</code>. This enables standard exception handling. You’ll learn about exceptions later, but without this flag, you will get lots of warnings:</p>

<pre data-type="programlisting" data-code-language="bash">cl<code class="w"> </code>/Wall<code class="w"> </code>/std:c++latest<code class="w"> </code>/EHsc<code class="w"> </code>empty.cpp<code class="w"></code></pre>

<p>You haven’t specified the output, so Windows chooses <em>empty.exe</em> for you based on the source file name.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Running your program"><div class="sect2" id="id46">
<h2>Running your program</h2>

<p>Run your program, using <code>.\empty.exe</code> on Windows or <code>./empty</code> on a Mac or Linux. You won’t see much, because the code doesn’t do anything. However, you are now set up and ready to learn more. You can run the program as often as you wish now, without needing to recompile and link the original source code.</p>

<p>Congratulations–you’ve compiled and linked your first program! Now let’s make it actually do something.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Writing to the screen"><div class="sect1" id="id47">
<h1>Writing to the screen</h1>

<p>You are now going to print “Hello, world!” to the screen. There is more than one way to do this in C++. I will show you two approaches here.</p>

<p>I noted that C++ has a new version every three years. C++23 introduced the function <code>println</code>. However, your toolchain might not support it. If this approach does not work for you, seeing the error output is useful, and you’ll learn an alternative approach shortly.</p>

<p>As you work your way through this book, you might find that some functions are not yet available in your compiler. If this happens, try using the Compiler Explorer instead.</p>








<section data-type="sect2" data-pdf-bookmark="Using println"><div class="sect2" id="id48">
<h2>Using <code>println</code></h2>

<p>The <code>println</code> function is part of the <em>standard library</em>. You used <code>int</code> above, which I noted is part of the core language. The standard library comes with your toolchain. You can use standard library facilities by including an appropriate header– in this case, <code>print</code>.</p>

<p>Recent versions (as I write this in late 2024) have introduced a new approach using <code>modules</code>, but many existing codebases still use the older method including headers, so let’s use that for now.</p>

<p>Create a new file called <em>hello_println.cpp</em>, either in your IDE or an editor, and add the <code>include</code> line and the empty <code>main</code> function as follows:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;print&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>If you include the items you want to use near the top of your file, you’ll be able to find them easily. You’ll usually see standard headers specified in angle brackets. Later on, you’ll include your own headers using double quotes.</p>

<p>This code is very similar to the first, with no code in <a data-type="xref" href="#first_main_code_block">Example 1-1</a>, apart from the <code>include</code> line, so it won’t do anything yet. Add your greeting using the <code>println</code> function from the <code>print</code> header. There are various ways to use <code>println</code>, but the simplest takes a single message in double quotes, like “Hello, world!”</p>

<p>Type the code into your <em>hello_println.cpp</em> file and save it:</p>
<div id="println_main_code_block" data-type="example">
<h5><span class="label">Example 1-2. </span>Using <code>println</code></h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;print&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">println</code><code class="p">(</code><code class="s">"Hello, world!"</code><code class="p">);</code><code class="w"> </code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>You have added a single line to the function body. Let’s think through what this line does.</p>

<p>The <code>println</code> function prints the message on a line and adds a newline character at the end. If you send messages to the screen, you might want further messages to start on the next line, so you’ll need a newline character at the end. The <code>println</code> function adds this for you.</p>

<p>Notice that the <code>println("Hello, world!")</code> instruction ends with a semicolon. In C++, every statement must be followed by a semicolon. This means that spacing isn’t significant. Some languages, like Python, use indentation and whitespace to denote blocks, but not in C++. The combination of semicolons to end a statement and braces to indicate where groups of code start and stop mean that whitespace is insignificant. However, people usually indent the function body (the contents between the curly braces) to make it stand out visually.</p>

<p>You need to put a function’s code inside its braces, which form its <em>block scope</em>. The concept of <em>scope</em> will crop up in various ways over the course of this book. In simple terms, it groups code together and allows magic to happen at the end of the block.</p>

<p>The <code>print</code> header also uses a different kind of scope: it groups code into a space with a name, called a <em>namespace</em>. All standard library facilities live inside the standard namespace, spelled <code>std</code>. You can prefix the function we need with <code>std::</code> to indicate where it comes from. This prefix consists of the name <code>std</code>, followed by the <em>scope-resolution operator</em>, <code>::</code>. You’re free to create your own namespaces, and doing so means you can specify where the compiler can find the function you want to use. Without <code>std::</code>, the compiler and linker will look for a function called <code>println</code> <em>outside</em> the standard namespace–and won’t find it. This leads to errors from the linker along the lines of <code>unresolved symbol</code>.</p>

<p>Save your file and build your code, using the warning and <code>std</code> version flags. Either find the “build and run” option in your IDE or use a prompt, as you did before, with the appropriate command for your toolchain:</p>

<pre data-type="programlisting" data-code-language="bash">g++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++23<code class="w"> </code>hello_println.cpp<code class="w"> </code>-o<code class="w"> </code>hello_println<code class="w"></code>


clang++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++2b<code class="w"> </code>hello_println.cpp<code class="w"> </code>-o<code class="w"> </code>hello_println<code class="w"></code>


cl<code class="w"> </code>/Wall<code class="w"> </code>/std:c++latest<code class="w"> </code>/EHsc<code class="w"> </code>hello_println.cpp<code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p>If you are using an IDE or visual editor, you can find instructions in its documentation for setting warning flags and the <code>std</code> language version.</p>
</div>

<p>If that worked, run your program. You should see a greeting on the screen:</p>

<pre data-type="programlisting" data-code-language="bash">Hello,<code class="w"> </code>world!<code class="w"></code></pre>

<p>Take a moment to look back at the code. You added an <code>include</code> to a standard library header. You only used one function here, but the standard library header contains more.</p>

<p>If you are using an IDE, you might be able to right-click into a menu; if so, go to the definition of <code>println</code> and have a look. You can also check <a href="https://en.cppreference.com/w/cpp/header/print">CppReference</a> to see what’s available via this standard library header. You will see various versions of <code>println</code>, including formats for aligning code and files, which I’ll show you later. There is a <code>print</code> function, too, which doesn’t add the newline character. Try this in your code instead of <code>println</code> and see what happens.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Troubleshooting"><div class="sect2" id="chp_01_troubleshooting">
<h2>Troubleshooting</h2>

<p>Your code might fail to compile. You might see a message like this:</p>

<pre data-type="programlisting">hello_println.cpp:1:10: fatal error: print: No such file or directory
    1 | #include &lt;print&gt;
      |          ^~~~~~~
compilation terminated.</pre>

<p>Or this:</p>

<pre data-type="programlisting">hello_println.cpp:1:10: fatal error: 'print' file not found
#include &lt;print&gt;
         ^~~~~~~
1 error generated.</pre>

<p>Or this:</p>

<pre data-type="programlisting">\include\print(11): warning STL4038:
  The contents of &lt;print&gt; are available only with C++23 or later.
hello_println.cpp(5): error C2039: 'println': is not a member of 'std'
predefined C++ types (compiler internal)(346): note: see declaration of 'std'
hello_println.cpp(5): error C3861: 'println': identifier not found</pre>

<p>If you see such a message, your toolchain does not support the <code>print</code> library features yet.</p>
<div data-type="tip"><h6>Tip</h6>
<p>In general, if you see a message saying that some feature in the namespace <code>std::</code> doesn’t exist, your toolchain may not support the feature you are trying to use.
You can use the Compiler Explorer instead. For example, try a <a href="https://godbolt.org/z/MErrs59xh">g++-14</a> build of the “hello world” code.</p>
</div>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Using cout"><div class="sect2" id="id50">
<h2>Using cout</h2>

<p>Before C++23, we used something called <code>std::cout</code> to print output instead of <code>println</code>. Many codebases still use this approach, and.
input uses a similar method, so the older way is worth learning and provides a useful starting point for the next chapter.</p>

<p>This standard library feature is declared in the <code>iostream</code> header.
The object <code>std::cout</code>, pronounced “see out,” is a global object associated with a <em>standard C output stream</em>. A <em>stream</em> in C++ is a sequence of characters. You may have used a stream processor before, such as <code>awk</code> or <code>sed</code>. <code>cout</code> handles character output and is limited to <a href="https://en.cppreference.com/w/cpp/language/ascii">American Standard Code for Information Interchange (ASCII)</a> characters, so it doesn’t handle Unicode characters.</p>

<p>Create a new file called <em>hello.cpp</em>. Add the <code>include</code> line and the <code>main</code> function, as you did before. This time, use the <code>std::cout</code> stream insertion operator <code>&lt;&lt;</code> to print the greeting, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Hello, world!"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>As before, you have to put the statement inside the main function’s braces and end the line with a semicolon. Putting some whitespace at the start of the line is conventional, but not required.</p>

<p>Build this single file. If you can’t remember what to do, refer back to the command-line instruction after <a data-type="xref" href="#println_main_code_block">Example 1-2</a>, changing the filename and output. Don’t forget to save your file and use the warning and <code>std</code> version flags.</p>

<p>When you run the new version, you will see that there is no new line after the greeting.
When I run the code from a prompt, I get the following:</p>

<pre data-type="programlisting" data-code-language="bash">$./hello<code class="w"></code>
Hello,<code class="w"> </code>world!$<code class="w"></code></pre>

<p>The greeting is displayed, and my prompt symbol appears on the same line, immediately after the message. In contrast, <code>println</code> prints the greeting <em>and</em> a new line. If you use <code>std::cout</code>, you need to ask for a new line if you want one. The simplest way to do this is to use <code>\n</code>. The backslash <em>escapes</em> the <code>n</code> for a new line, meaning it indicates that the character after the backslash has a special meaning. There are several other escape sequences, including <code>\t</code> for tab, and <code>\\</code> for a single backslash.</p>

<p>You can add the extra character to the greeting:</p>
<div id="cout_main_code_block" data-type="example">
<h5><span class="label">Example 1-3. </span>Using cout.</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Hello, world!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>When you build and run this code, your prompt starts on a new line:</p>

<pre data-type="programlisting" data-code-language="bash">$./hello<code class="w"></code>
Hello,<code class="w"> </code>world!<code class="w"></code>
$<code class="w"></code></pre>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding println and cout in depth"><div class="sect1" id="chap01_in_depth">
<h1>Understanding <code>println</code> and <code>cout</code> in depth</h1>

<p>You should have at least one “Hello, world!” program working now. Before moving on, let’s take a deeper look at what you’ve done so far.</p>

<p>You started with a main function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>As with most functions, it has a return type and parenthesis, along with a name.
Any statements go inside the braces.</p>

<p>You used two functions from the standard library. First, you used <code>println</code>. There are several versions (or <em>overloads</em>), but they all have a similar signature:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">println</code><code class="p">();</code><code class="w"></code>
<code class="kt">void</code><code class="w"> </code><code class="nf">println</code><code class="p">(</code><code class="cm">/* maybe some parameters*/</code><code class="p">);</code><code class="w"></code></pre>

<p>An <em>overloaded function</em> has the same name, but takes different parameters. For example, you can use <code>println</code> for an <code>int</code> or a message. The definitions of these two different versions take different parameter types. Each function head is followed by a semicolon and <em>declares</em> the function: that is, it tells the compiler that this function exists somewhere. The linker will try to find the matching function <em>definition</em>, which has the function’s contents in curly braces.</p>

<p>Remember, <code>main</code> returns an <code>int</code>. The <code>println</code> function starts with <code>void</code>, meaning that it doesn’t return anything. You also used <code>cout</code>, along with the stream insertion operator <code>&lt;&lt;</code> to display “Hello, world!” There is an overload for <code>int</code> and other types.
Every overload returns something. <a href="https://en.cppreference.com/w/cpp/io/basic_ostream/operator_ltlt">CppReference</a> gives a long list of overloads. Here are a couple:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">basic_ostream</code><code class="o">&amp;</code><code class="w"> </code><code class="k">operator</code><code class="o">&lt;&lt;</code><code class="p">(</code><code class="kt">int</code><code class="w"> </code><code class="n">value</code><code class="p">);</code><code class="w"></code>
<code class="n">basic_ostream</code><code class="o">&amp;</code><code class="w"> </code><code class="k">operator</code><code class="o">&lt;&lt;</code><code class="p">(</code><code class="cm">/*various other types of parameters*/</code><code class="p">);</code><code class="w"></code></pre>

<p>Again, you can see the return type, function name, and parentheses. The return type is a <code>basic_ostream</code> (<code>o</code> stands for “output”). You also used <code>std::cout</code>, noting that it is a stream. A <code>basic_ostream</code> is more general than <code>std::cout</code>, so it can be applied to other streams. I’ll show you more stream examples, including the input stream, in the next chapter.</p>

<p>There are two special terms to note in the stream insertion operator’s declaration:</p>

<ul>
<li>
<p>the word <em>operator</em></p>
</li>
<li>
<p>the <code>&amp;</code> symbol</p>
</li>
</ul>

<p>An <em>operator</em> is a special type of function made up of a symbol, like + or &lt;&lt;, that you can use between the <em>operands</em> or <em>arguments</em>: that is, the specific parameters to which you want to apply the operator. They can make code more readable. For example, it’s more natural to say <code>1+2</code> than <code>operator+(1, 2)</code>.</p>

<p>The <code>&amp;</code> symbol denotes a <em>reference</em> to an object. I’ll provide more details later, but for now, when you see <code>&amp;</code>, just know that the code is referring to something that already exists (in this case, <code>std::cout</code>).  Without the reference, the function would return a new object.</p>

<p>You added the <code>\n</code> character inside the greeting <code>"Hello, world!\n"</code>, but now you have an alternative. The stream insertion operator returns the same stream you started with, so you can <em>chain</em> calls together. You can also chain operations: for example, you can chain additions to calculate 1 + 2 + 3.
To append the newline character, use <code>&lt;&lt;</code> again, with single quotes for the single character:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Hello, world!"</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code></pre>

<p>The compiler parses the statement from left to right. It takes the first message, then applies the <code>&lt;&lt;</code> operator a second time with the newline character:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Hello, world!"</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code></pre>

<p>You don’t need brackets here, since the two statements are equivalent.</p>

<p>You can chain lots of different messages together. You might find that the compiler writes out very long messages in chunks. Since it takes a while to render the characters on a screen, it buffers them and writes a few at a time.</p>

<p>You will sometimes see <code>std::endl</code> used instead of <code>\n</code>. That does two things:</p>

<ul>
<li>
<p>appends a new line</p>
</li>
<li>
<p>flushes the buffer</p>
</li>
</ul>

<p><em>Flushing</em> the buffer ensures that everything in the buffer is written. If a program crashes without doing this, any buffered characters may never make it to the stream. <code>std::endl</code> is a helper function to control a stream, called a <em>manipulator</em>, and using it is equivalent to the following:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">flush</code><code class="p">;</code><code class="w"></code></pre>

<p>You are unlikely to need to do this, but if your code crashes, you might not see all the output unless you have flushed the buffer beforehand. You may come across other learning resources telling you to use <code>std::endl</code>; now you know that you have a choice.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id52">
<h1>Conclusion</h1>

<p>This chapter covered the difference between interpreted and compiled languages. C++ is compiled. The compiler reads the source code, parsing it into object files, then linking them together into a program.
You also set up your C++ toolchain and built some code.You wrote one function (a few times):</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>You also used two functions from the standard library:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">std::println</code><code class="p">(</code><code class="cm">/* maybe some parameters*/</code><code class="p">);</code><code class="w"></code>
<code class="n">basic_ostream</code><code class="o">&amp;</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="k">operator</code><code class="o">&lt;&lt;</code><code class="p">(</code><code class="cm">/*maybe some parameters*/</code><code class="p">);</code><code class="w"></code></pre>

<p>To use these, you included the appropriate headers.</p>

<p>These three functions introduced some important ideas:</p>

<ul>
<li>
<p>Functions have a head and a body; the body goes inside curly braces.</p>
</li>
<li>
<p>Statements end in a semicolon.</p>
</li>
<li>
<p><code>main</code> is special and where a program starts.</p>
</li>
<li>
<p><code>int</code> is a built-in type for positive and negative numbers.</p>
</li>
<li>
<p><code>void</code> indicates that a function returns nothing.</p>
</li>
<li>
<p>The reference symbol, <code>&amp;</code>, refers to something that already exists.</p>
</li>
<li>
<p><code>std</code> indicates the standard namespace.</p>
</li>
<li>
<p><code>::</code> is the scope-resolution operator.</p>
</li>
<li>
<p>There are various types of streams; you’ve now used <code>std::cout</code>.</p>
</li>
</ul>

<p>You have written your first C++ program. You’ve learned some basic syntax and how to call some standard library functions. You’ve written output, so you’re probably wondering: how do you take input? Let’s find out.</p>
</div></section>
</div></section></div></div></body></html>