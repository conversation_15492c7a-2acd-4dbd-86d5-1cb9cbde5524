<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 5. Using Standard Library Algorithms"><div class="chapter" id="chapter_five">
<h1><span class="label">Chapter 5. </span>Using Standard Library Algorithms</h1>

<aside data-type="sidebar" epub:type="sidebar"><div class="sidebar" id="id120">
<h1>A Note for Early Release Readers</h1>
<p>With Early Release ebooks, you get books in their earliest form—the author’s raw and unedited content as they write—so you can take advantage of these technologies long before the official release of these titles.</p>

<p>This will be the 5th chapter of the final book.</p>

<p>If you have comments about how we might improve the content and/or examples in this book, or if you notice missing material within this chapter, please reach out to the editor at <em><EMAIL></em>.</p>
</div></aside>

<p>You got several numbers through input in the last chapter, and found the largest value in an array.
In this chapter, you will do exactly the same thing using some standard library functions.
I will also show you how to organize your code sensibly, so you can reuse functions in different programs.</p>

<p>Let’s give the numbers a meaning: stock prices.
Armed with a set of stock prices, you can find the largest, the smallest, and other properties.
Over the rest of this book, I will show you how to simulate prices, read them from a file, and build a portfolio of stocks.</p>






<section data-type="sect1" data-pdf-bookmark="Getting several numbers into a vector (again)"><div class="sect1" id="id95">
<h1>Getting several numbers into a vector (again)</h1>

<p>In the last chapter, you put numbers into an <code>array</code>, then into a <code>vector</code>.
The <code>vector</code> allows you to store a varying number of values, so it tends to be most people’s default choice of sequential container.</p>

<p>You’re going to reuse your code from the last chapter, but reorganize it to make it easy to reuse.
Each source file you have written so far has its own <code>main</code> function.
Now, an application can only have one <code>main</code>, but can include several source files.
In this section, you will write two source files with extension <em>.cpp</em>, and your own header file, with extension <em>.h</em>.
One source file will have the usual <code>main</code> function and will use the code from the second source file.</p>
<div data-type="tip"><h6>Tip</h6>
<p>It is a best practice to use the <em>.cpp</em> extension for your source files. You don’t have to, and some people use <em>.cc</em> or <em>.cxx</em> or something else instead. However, consistency makes your code’s structure clearer. The header extension doesn’t matter either, and people often use <em>.hpp</em> instead of <em>.h</em>. I will use <em>.cpp</em> for source files and <em>.h</em> for headers, but you might see other conventions elsewhere.</p>
</div>

<p>If you put declarations in header files, you can use them in other source files that include your header.
You could declare the functions and objects you want to use in the source file directly.
However, you might also need to use the same declaration in each of your source files.
Adding the same code to each source file isn’t very sensible–repetitive code is tedious and error prone. Using a header file is a much better approach.</p>

<p>When you include a header file, the code from that file gets copied in place of the <code>#include</code> line, so you avoid the need to copy and paste code.
Think of the <code>#include</code> as saying “copy the contents of the header here,” as shown in <a data-type="xref" href="#fig_5_1">Figure 5-1</a>.</p>

<figure><div id="fig_5_1" class="figure">
<img src="assets/fig_5_1.png" alt="Including a header" width="1280" height="720"/>
<h6><span class="label">Figure 5-1. </span>Including a header.</h6>
</div></figure>

<p>Start with a new file, and call it <em>input.h</em>.
You will declare the <code>get_number</code> function you wrote before in this file.
Look back at the last chapter for <a href="ch04.html#chap_4_vector_of_numbers">the code to get several numbers in a vector</a>, and take note of the function signature for <code>get_number</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">);</code><code class="w"></code></pre>

<p>Copy this into your header file, so you can use it from your main source file.  The function’s definition will go in a source file, so you can reuse the header and source files (and you’ll need to do so in future chapters).</p>

<p>Your header needs a little more than just the function declaration, though.
Let’s think through what else is needed. First, any given header file might be included by several source files or header files.
In fact, you could even include your header file <em>in the header file itself.</em>  A silly thing to do, but if you did, what would happen?
Imagine drawing a version of <a data-type="xref" href="#fig_5_1">Figure 5-1</a> where the header file includes itself!  The compiler would keep seeing a line asking for the header to be included, open the header, see the same include line, and so on. Fortunately, your header can indicate that it should only be included once.
There are a few ways to do this, but writing <code>#pragma once</code> at the top of the file works.</p>
<div data-type="tip"><h6>Tip</h6>
<p>Adding a <code>pragma</code> statement is sometimes called a <em>pragma directive</em>.
These directives allow you to use compiler-specific features, though this particular one, <code>#pragma once</code>, works on almost all toolchains.
Like the <code>include</code> statement, the pragma directive line starts with a #. These are two of several directives used before compilation, which are collectively known as <em>preprocessing</em>.
For example, you saw how <code>#include</code> statements copy in the contents of each included file before compilation.</p>
</div>

<p>So, your header file needs <code>#pragma once</code> as its first line.</p>

<p>Next, your declaration of <code>get_number</code> will use <code>std::expected</code>, <code>std::string</code>, and <code>std::istream</code>; include those headers too, so the declaration makes sense and the code (including the header) can find it for the types used.
If you included just your header but not the includes, your code would error, saying it doesn’t know what <code>std::expected</code> and so on are.</p>

<p>Finally, you will declare your function, <code>get_number</code>.
Now, you met namespaces in the first chapter. You’ve also seen that every time you use something from the standard library, you add <code>std::</code> at the start.
Putting your functions inside your own namespace is sensible.
A namespace groups related code together and gives your code structure.
Namespaces also restrict scope, which lets you avoid having two functions with the same signature. So let’s put the function in a <code>stock_prices</code> namespace.
(You <em>can</em> overload functions by giving them the same name but different parameters. However, two functions can’t have the exact same signature. If that was allowed, how would the linker know which one to use?)</p>

<p>Pulling this together gives you the header file shown in <a data-type="xref" href="#first_header_file">Example 5-1</a>.</p>
<div id="first_header_file" data-type="example">
<h5><span class="label">Example 5-1. </span>Your first header file, input.h.</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">pragma once </code><a class="co" id="co_using_standard_library_algorithms_CO1-1" href="#callout_using_standard_library_algorithms_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO1-2" href="#callout_using_standard_library_algorithms_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;istream&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO1-3" href="#callout_using_standard_library_algorithms_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;string&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO1-4" href="#callout_using_standard_library_algorithms_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO1-5" href="#callout_using_standard_library_algorithms_CO1-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO1-6" href="#callout_using_standard_library_algorithms_CO1-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code><code class="w"> </code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO1-1" href="#co_using_standard_library_algorithms_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Guards against multiple inclusions</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO1-2" href="#co_using_standard_library_algorithms_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes <code>std::expected</code>, <code>std::iostream</code>, and <code>std::string</code>, used in the function declaration</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO1-3" href="#co_using_standard_library_algorithms_CO1-5"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Opens a namespace, where any code between the curly braces will live</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO1-4" href="#co_using_standard_library_algorithms_CO1-6"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Declares a function inside the namespace</p></dd>
</dl></div>

<p>You have declared a function. Now you need to define it. Create a new source file called <em>input.cpp</em> for the definition.
People usually give header files the same name as the source file, with a different extension.
If you see code that includes <em>input.h</em>, you can expect the definitions to be in a file called <em>input.cpp</em>.</p>

<p>The <code>get_number</code> definition will go in your new source file, but first, there are a couple of things you need to think about.
The function uses <code>std::numeric_limits</code>, so you’ll need to include <code>&lt;limits&gt;</code>.
You didn’t <em>need</em> to put this in your header file, because it isn’t in the function declaration and thus wouldn’t be used.  Adding unused headers isn’t a disaster, but it can slow your build down a little, which can make a difference for a very large code base.
Instead, include the <code>&lt;limits&gt;</code> header in <em>input.cpp</em>.</p>

<p>You are also using <code>std::expected</code>, <code>std::string</code>, and <code>std::istream</code>, all of which you included these in  <em>input.h</em>, so including the <em>input.h</em> file in <em>input.cpp</em> will make the includes visible to the source file.
Put your own header includes in double quotes <code>""</code> rather than the angle brackets <code>&lt;&gt;</code> you use for library headers: <code>#include "input.h"</code>.</p>

<p>Second, you’ve declared <code>get_number</code> in a namespace, so you need to put its definition in that namespace too.
Pulling this together gives you the code shown in <a data-type="xref" href="#first_separate_source_file">Example 5-2</a>.</p>
<div id="first_separate_source_file" data-type="example">
<h5><span class="label">Example 5-2. </span>A separate source file called input.cpp.</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO2-1" href="#callout_using_standard_library_algorithms_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO2-2" href="#callout_using_standard_library_algorithms_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO2-3" href="#callout_using_standard_library_algorithms_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO2-4" href="#callout_using_standard_library_algorithms_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w"> </code><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">streamsize</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w">
</code><code class="w">            </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w">
</code><code class="w">        </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"</code><code class="s">That's not a number</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO2-1" href="#co_using_standard_library_algorithms_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes limits</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO2-2" href="#co_using_standard_library_algorithms_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes your header file (notice the quotes rather than angle brackets)</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO2-3" href="#co_using_standard_library_algorithms_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Opens a namespace</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO2-4" href="#co_using_standard_library_algorithms_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Defines the function</p></dd>
</dl></div>

<p>Now that you have a function in defined a separate source file, with a declaration in a header, you can use it in any program you write.
Just include your header in any source files you want to use the function, and add the source file to your build instructions.
I’ll show you how to do that shortly. First, you need to use the function and provide a <code>main</code> somewhere.</p>

<p>Create a new source file and call it <em>main.cpp</em>.
Now you’re going to write a function based on the <code>main</code> <a href="ch04.html#chap_4_vector_of_numbers">function</a> from the last chapter.</p>

<p>First, include your header file, <em>“input.h”</em>, and then add a function that will fetch prices. Previously, you wrote this inside the <code>main</code> function, getting numbers in a <code>while</code> loop, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code>
<code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">())</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">)</code><code class="o">:</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Got "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">()</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" thanks!</code><code class="se">\n</code><code class="s">&gt;"</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Now that you’re writing a separate function, you will return a <code>std::vector&lt;double&gt;</code> and take a <code>std::istream</code> to read numbers from.
The <code>get_number</code> function is in the namespace <code>stock_prices</code>, so you need to specify <code>stock_prices::</code> before the function call.
Pulling all this together gives you the code in <a data-type="xref" href="#main_using_function_in_other_file">Example 5-3</a>.</p>
<div id="main_using_function_in_other_file" data-type="example">
<h5><span class="label">Example 5-3. </span>Calling a function in another source file to get several numbers.</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO3-1" href="#callout_using_standard_library_algorithms_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO3-2" href="#callout_using_standard_library_algorithms_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO3-3" href="#callout_using_standard_library_algorithms_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">   </code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">numbers</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO3-4" href="#callout_using_standard_library_algorithms_CO3-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO3-1" href="#co_using_standard_library_algorithms_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes your header</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO3-2" href="#co_using_standard_library_algorithms_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Uses <code>get_number</code> from the <code>stock_prices</code> namespace</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO3-3" href="#co_using_standard_library_algorithms_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Add a <code>&gt;</code> to the output so user knows to enter something else</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO3-4" href="#co_using_standard_library_algorithms_CO3-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Calls <code>get_prices</code> using <code>std::cin</code></p></dd>
</dl></div>

<p>To build both source files, specify both their names.
Previously in building code, you’ve used the warning and language version flags, then stated one <em>.cpp</em> file, followed by the output. This time,  you’ll state two <em>.cpp</em> files.
For example, if you’re using g++:</p>

<pre data-type="programlisting" data-code-language="bash">g++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++23<code class="w"> </code>input.cpp<code class="w"> </code>main.cpp<code class="w"> </code>-o<code class="w"> </code>stock_prices<code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p>There are various build systems, such as Makefiles and CMake, that allow you to use a single instruction without having to remember to list all the relevant source files. They are beyond the scope of this book, but you can find many tutorials on the internet.</p>
</div>

<p>Now you can run your program. Try entering a few numbers, and finish by entering some nonnumeric input, like “bye”.</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;2<code class="w"></code>
&gt;3.4<code class="w"></code>
&gt;-1<code class="w"></code>
&gt;5<code class="w"></code>
&gt;4.56789<code class="w"></code>
&gt;bye<code class="w"></code></pre>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Analyzing your numbers using algorithms"><div class="sect1" id="Chap5_Analysis">
<h1>Analyzing your numbers using algorithms</h1>

<p>It would be sensible to do something with the  numbers you entered, wouldn’t it?
You fetched the prices in <em>main.cpp</em>, inside the <code>main</code> function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code></pre>

<p>Include <code>&lt;algorithm&gt;</code> at the top of your <em>main.cpp</em> file.
This is useful in several ways.
C++ <em>ranges</em> allow you to use a whole container, or just part of one, easily.
Ranges were introduced in C++20, and there are still older versions of algorithms in the library.
Some algorithms have both versions, but others aren’t supported by ranges yet.</p>

<p>Let’s look at how to use range algorithms and classic algorithms.  You’ll begin by finding the largest and smallest of your values.
Previously you found the biggest number in a container <a href="ch04.html#find_max_by_hand">using a loop</a>.
Rather than using a range-based <code>for</code> loop here, you can use an algorithm from the C++ standard library called <code>minmax</code>, which finds the largest and smallest values. The <code>result</code> has two values, <code>min</code> and <code>max</code>, telling you the values you want.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>If you don’t enter any numbers, the range will be empty and the behavior of <code>minmax</code> will thus be undefined. gcc gives a “Segmentation fault” error message, but other toolchains may behave differently.</p>
</div>

<p>Add the call to <code>main</code> and display the values, provided the range isn’t empty:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO4-1" href="#callout_using_standard_library_algorithms_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">numbers</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO4-2" href="#callout_using_standard_library_algorithms_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">result</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">minmax</code><code class="p">(</code><code class="n">prices</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO4-3" href="#callout_using_standard_library_algorithms_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">min </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">result</code><code class="p">.</code><code class="n">min</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO4-4" href="#callout_using_standard_library_algorithms_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">max </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">result</code><code class="p">.</code><code class="n">max</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO4-5" href="#callout_using_standard_library_algorithms_CO4-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO4-1" href="#co_using_standard_library_algorithms_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes algorithms</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO4-2" href="#co_using_standard_library_algorithms_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Checks that there are prices, not an empty range</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO4-3" href="#co_using_standard_library_algorithms_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Finds the biggest and smallest elements</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO4-4" href="#co_using_standard_library_algorithms_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Prints the smallest price</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO4-5" href="#co_using_standard_library_algorithms_CO4-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Prints the largest price</p></dd>
</dl>

<p>If you build and run your code, you will now see some output:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;2<code class="w"></code>
&gt;3<code class="w"></code>
&gt;5.6<code class="w"></code>
&gt;-9<code class="w"></code>
&gt;bye<code class="w"></code>
min<code class="w"> </code>-9<code class="w"></code>
max<code class="w"> </code><code class="m">5</code>.6<code class="w"></code></pre>

<p>You’ve found the minimum and maximum values.</p>








<section data-type="sect2" data-pdf-bookmark="Using predicates in algorithms"><div class="sect2" id="id97">
<h2>Using predicates in algorithms</h2>

<p>The <code>minmax</code> algorithm is a <em>search</em> algorithm.
There are many more, including some that allow you to look for elements that fulfil certain criteria.
This is provided by a <em>predicate</em>: a function that returns true or false for a value.
C++ algorithms provide a few ways to search and filter out values.
For example, a negative stock price seems unlikely, so you might want to remove any of those before starting further analysis.</p>

<p>Let’s see if any prices have negative values. Create two new files: <em>analysis.cpp</em> and <em>analysis.h</em>.
Then you’ll write a predicate to decide if a number is negative.  It only needs one line.  You could put this predicate function in your new source file, but people often put short functions in headers, which is called defining them <em>inline</em>.  They can then be copied directly into calling code, without the need for the overhead of a function call, which can make the code run quicker. In this case, you’ll add the code to your header, and then you’re compare a double against 0.0.</p>
<div data-type="tip"><h6>Tip</h6>
<p>The term <em>translation unit</em> refers to a source file and the headers it includes. You can only define one function, variable, or other type per translation unit. There must only be one single, unambiguous definition for such things in the whole resulting program. This is called the <em>one-definition rule</em> (ODR).</p>

<p>If you don’t add <code>inline</code> to a function that you place in a header, each C++ file that uses the header will have its own copy of the function, which will break the ODR. Adding <code>inline</code> stops this error from happening.
I recommend watching <a href="https://www.youtube.com/watch?v=HakSW8wIH8A">Roger Orr’s</a> talk about the ODR from the ACCU 2024 conference, if you want more details. ACCU is a group  of programmers who care about their craft, and historically stood for the Association of C and C++ Users. They still cover C++ but more besides.</p>
</div>

<p>Use the <code>stock_prices</code> namespace again in your new analysis.h file, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">pragma once </code><a class="co" id="co_using_standard_library_algorithms_CO5-1" href="#callout_using_standard_library_algorithms_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO5-2" href="#callout_using_standard_library_algorithms_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kr">inline</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="nf">negative</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO5-3" href="#callout_using_standard_library_algorithms_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code><code class="w"> </code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO5-1" href="#co_using_standard_library_algorithms_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Guards against multiple inclusion</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO5-2" href="#co_using_standard_library_algorithms_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Reopens the namespace</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO5-3" href="#co_using_standard_library_algorithms_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Defines a short function inline</p></dd>
</dl>

<p>The predicate <code>negative</code> is a <em>unary</em> predicate, meaning that it takes just one parameter, <code>value</code>.</p>

<p>In your <em>analysis.cpp</em> file, include your header file, <em>analysis.h</em>, at the top, ready for later additions.
Now you can use your <code>negative</code> function from <code>main</code>.</p>

<p>You’ll start by counting any negative prices.
Ranges provide a function called <code>count_if</code>, via the <code>algorithm</code> header.
This takes a range and a predicate, so you can use your <code>negative</code> function.
Add the code to <code>main</code> like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-1" href="#callout_using_standard_library_algorithms_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">numbers</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-2" href="#callout_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO6-3" href="#callout_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">result</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">minmax</code><code class="p">(</code><code class="n">prices</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-4" href="#callout_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">min </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">result</code><code class="p">.</code><code class="n">min</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-5" href="#callout_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">max </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">result</code><code class="p">.</code><code class="n">max</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-6" href="#callout_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">count_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">negative</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO6-7" href="#callout_using_standard_library_algorithms_CO6-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> prices below zero</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO6-1" href="#co_using_standard_library_algorithms_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes your new header</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO6-2" href="#co_using_standard_library_algorithms_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Gets prices and find the min and max as before</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO6-3" href="#co_using_standard_library_algorithms_CO6-7"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Calls <code>count_if</code> to find negative numbers</p></dd>
</dl>

<p>Just pause for a moment, and appreciate this single line to count negative prices:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">ranges</code><code class="o">::</code><code class="n">count_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code></pre>

<p>You could write a loop to count these yourself, but doing so requires some thought, and you might make mistakes.
Using the algorithm instead gives you clearer code and reduces the possibility of introducing errors.
You can also tell what it’s doing: counting any negative prices.</p>

<p>Build your code again and try it out. Your program will now count any negative numbers you entered, as well as finding the minimum and maximum values:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;1.0<code class="w"></code>
&gt;1.2<code class="w"></code>
&gt;-0.5<code class="w"></code>
&gt;3<code class="w"></code>
&gt;done<code class="w"></code>
min<code class="w"> </code>-0.5<code class="w"></code>
max<code class="w"> </code><code class="m">3</code><code class="w"></code>
<code class="m">1</code><code class="w"> </code>prices<code class="w"> </code>below<code class="w"> </code>zero<code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p>You made a new source file, <em>analysis.cpp</em>, and included the <em>analysis.h</em> file.
The source file has nothing else in it yet.
The function is defined  <code>inline</code> in the header, so it is available whenever the header file is included.
At the moment, you don’t need to list the source in your build instructions. However, when you add functions into <em>analysis.cpp</em>, you will need to add it in the build instructions.
I’ll remind you when you need to do this.</p>
</div>

<p>Next, you can remove any negative values, and then find the average price.</p>

<p>C++20 introduced a function to erase elements according to a predicate, called <code>erase_if</code>.
It returns the number of elements it erased, so you can use it to count the negative values as you erase them:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">erased</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">erase_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w">  </code><code class="n">erased</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" prices below zero</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code></pre>

<p>Some codebases still use older C++ versions, so before we move on, I’ll show you how to remove elements from a container without using the <code>erase_if</code> function.
You will learn more C++ as you read this, even if you decide that <code>erase_if</code> is much simpler.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Using iterators in algorithms"><div class="sect2" id="iterators">
<h2>Using iterators in algorithms</h2>

<p>At the start of <a data-type="xref" href="#Chap5_Analysis">“Analyzing your numbers using algorithms”</a>, I mentioned that some algorithms don’t use ranges yet.
Many do, but it’s worth being able to use the older versions as well.</p>

<p>When you called <code>minmax</code> and <code>count_if</code>, you passed the <code>prices</code> container  to the algorithms.
The older versions of these algorithms take two <em>iterators</em>, which you started to learn about in <a data-type="xref" href="ch04.html#vector_insert">Example 4-11</a>.
Many languages have the idea of an iterator. “An <em>iterator</em> is an abstract view of a position in a sequence that’s independent of both the type of the elements and the sequence itself.”<sup><a data-type="noteref" id="id121-marker" href="ch05.html#id121">1</a></sup>
For example, you can create a <code>std::array</code> and get <code>begin</code> and <code>end</code> iterators :</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">array</code><code class="w"> </code><code class="n">whole_numbers</code><code class="p">{</code><code class="mi">1</code><code class="p">,</code><code class="mi">2</code><code class="p">,</code><code class="mi">3</code><code class="p">};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">numbers_begin</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">whole_numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">();</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">numbers_end</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">whole_numbers</code><code class="p">.</code><code class="n">end</code><code class="p">();</code><code class="w"></code></pre>

<p>You could do likewise with a <code>std::vector</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">prices</code><code class="p">{</code><code class="mf">1.01</code><code class="p">,</code><code class="w"> </code><code class="mf">2.02</code><code class="p">,</code><code class="w"> </code><code class="mf">3.03</code><code class="p">};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">prices_begin</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">();</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">prices_end</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">();</code><code class="w"></code></pre>

<p>You can use these iterators in any algorithm, even though the containers are different and contain different types of elements.</p>

<p>The iterator indicates the element’s position in the container.
To refer to the elements, you need to use the asterisk *, called a <em>dereference</em> operator, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="o">*</code><code class="n">numbers_begin</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO7-1" href="#callout_using_standard_library_algorithms_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="o">*</code><code class="n">prices_begin</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO7-2" href="#callout_using_standard_library_algorithms_CO7-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO7-1" href="#co_using_standard_library_algorithms_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Gives the value of the <code>int</code> at the position indicated by the <code>array_iterator</code></p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO7-2" href="#co_using_standard_library_algorithms_CO7-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Gives the value of the <code>double</code> at the position indicated by the <code>vector_iterator</code></p></dd>
</dl>

<p>In <a data-type="xref" href="ch04.html#Chap4_erase_from_vector">“What happens when you delete from a vector”</a>, you erased a few elements from a vector, starting with the first item.
You also used <code>begin</code> to find an iterator to the first element.
By convention, the second iterator is one past the last element on which you want to perform the algorithm.
The pair of iterators form a <em>half open range</em>, a term borrowed from mathematics.
A mathematical <em>closed range</em>, like [1, 3], includes the 1 and 3 at the beginning and end, so that means it includes the whole numbers 1, 2, and 3.
In contrast, an <em>open range</em>, written with curved brackets, like (1, 3), doesn’t include the 1 or 3, just what’s between them. So the only whole number it would include is 2.
<a data-type="xref" href="#fig_5_2">Figure 5-2</a> shows square brackets including a number and curved brackets not including the number, for closed, open and half open ranges.</p>

<figure><div id="fig_5_2" class="figure">
<img src="assets/fig_5_2.png" alt="Mathematical open and closed ranges" width="1280" height="720"/>
<h6><span class="label">Figure 5-2. </span>Mathematical open and closed ranges</h6>
</div></figure>

<p>The pair of iterators used by an algorithm are a <em>half open range</em>, which includes <code>begin</code> and everything up to, but <em>not</em> including, <code>end</code>. It’s written as <code>[begin, end)</code>.</p>

<p>(You don’t need to know the math terminology, but it does get used in documentation from time to time.)
You saw the <code>begin</code> and <code>end</code> of a <code>std::vector</code> in <a data-type="xref" href="ch04.html#fig_4_5">Figure 4-5</a>.
Any algorithm that takes two iterators starts at the first and stops when it reaches the second, without using that second iterator.
However, the iterators don’t need to be <code>begin</code> and <code>end</code>: instead of the whole container, you could use part of the container.</p>

<p>Recall how you used <code>count_if</code> from ranges:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">ranges</code><code class="o">::</code><code class="n">count_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code></pre>

<p>You can pass your prices’ <code>begin</code> and <code>end</code> elements to the older function instead:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">=</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">count_if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code></pre>

<p>Both approaches operate on the whole container.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="The old way to remove items"><div class="sect2" id="id99">
<h2>The old way to remove items</h2>

<p>The standard library algorithms also provide <code>remove_if</code> functions, both for ranges and for pairs of iterators.
You might see them used, so it’s worth knowing what they do and why.</p>

<p>You’ll use the iterator version in this section, and you can try the range version yourself afterwards.</p>

<p>You are going to write a function in your <em>analysis.cpp</em> file, using <code>std::remove_if</code>.
Let’s discover how it works.
The call itself is very similar to <code>count_if</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">something</code><code class="w"> </code><code class="o">=</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">remove_if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code></pre>

<p>What will the algorithm return?
To answer that question, we need to consider what this algorithm actually does.
Despite its name, it doesn’t actually remove any elements.
Your container will remain the same size!
Instead, this algorithm shifts the elements you want to keep towards the beginning of the container.
The elements you want to keep start at <code>begin</code> and go up to a new <code>end</code>, which is the returned value.
So, the mysterious <code>something</code> in the code above is an iterator, pointing one past the end of the elements you want.
What is left after that is unspecified.
You can then use the returned value instead of the <code>end</code> to refer to the elements you want.
<a data-type="xref" href="#fig_5_3">Figure 5-3</a> shows what happens.</p>

<figure><div id="fig_5_3" class="figure">
<img src="assets/fig_5_3.png" alt="Removing shuffles wanted elements to the front" width="1280" height="720"/>
<h6><span class="label">Figure 5-3. </span>Removing negative elements</h6>
</div></figure>

<p>This isn’t exactly intuitive at first sight.
However, the original algorithms take pairs of iterators, because doing so is more general than using an implementation of each algorithm for each container.
Using a pair of iterators means you cannot adapt the size of the container.
But since this returns the new <code>end</code>, your calling code can instead use a subsequent call to <code>erase</code> to change the container directly.</p>

<p>Let’s add tests while writing a function to remove the invalid elements, which we’ll call <code>remove_invalid</code>.
In <em>analysis.h</em>, add declarations for the new function and for your tests in the namespace:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kr">inline</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="nf">negative</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO8-1" href="#callout_using_standard_library_algorithms_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="nf">test_analysis</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO8-2" href="#callout_using_standard_library_algorithms_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO8-1" href="#co_using_standard_library_algorithms_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Declares the removal function</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO8-2" href="#co_using_standard_library_algorithms_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Declares a test function</p></dd>
</dl>

<p>You will add the <code>test_analysis</code> function in the <em>analysis.cpp</em> file, but you’ll call it from <code>main</code>.
Use the <code>cassert</code> header, as you did before in <a data-type="xref" href="ch02.html#failing_test">“Starting with a failing test”</a>, to validate your code.
You can make a <code>vector</code> using one valid and one invalid value, like <code>{-1.2, 3.5}</code>.
The removal function needs to return a <code>vector</code> with one fewer element.
See if you can get most of the way to writing this function on your own.
If not, use the code in <a data-type="xref" href="#remove_without_erase">Example 5-4</a>.</p>
<div id="remove_without_erase" data-type="example">
<h5><span class="label">Example 5-4. </span>Removing invalid elements (Work in Progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="c1">  </code><a class="co" id="co_using_standard_library_algorithms_CO9-1" href="#callout_using_standard_library_algorithms_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="c1">  </code><a class="co" id="co_using_standard_library_algorithms_CO9-2" href="#callout_using_standard_library_algorithms_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO9-3" href="#callout_using_standard_library_algorithms_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO9-4" href="#callout_using_standard_library_algorithms_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">remove_if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><code class="n">negative</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">prices</code><code class="p">;</code><code class="w">	</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="n">test_analysis</code><code class="p">(</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO9-5" href="#callout_using_standard_library_algorithms_CO9-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">got</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="p">{</code><code class="mf">-1.2</code><code class="p">,</code><code class="w"> </code><code class="mf">3.5</code><code class="p">}</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO9-6" href="#callout_using_standard_library_algorithms_CO9-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mf">3.5</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-1" href="#co_using_standard_library_algorithms_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes C’s assert function</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-2" href="#co_using_standard_library_algorithms_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes your own header</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-3" href="#co_using_standard_library_algorithms_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Reopens the <code>stock_prices</code> namespace</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-4" href="#co_using_standard_library_algorithms_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Function to remove invalid elements</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-5" href="#co_using_standard_library_algorithms_CO9-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Defines the tests</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO9-6" href="#co_using_standard_library_algorithms_CO9-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Uses an initializer list with two values to make a <code>std::vector</code></p></dd>
</dl></div>

<p>Did you notice that the <code>remove_invalid</code> function uses pass by value to take the <code>prices</code> as a copy?
Perhaps using pass by value seems more sensible now.
You have passed by reference before using a <code>const &amp;</code>: for example, in <a data-type="xref" href="ch03.html#try_or_catch_exception">Example 3-2</a>.
The <code>const</code> means you won’t change the values, and the reference, <code>&amp;</code>, avoids copying the data.
This time, though, you <em>do</em> want to change the values.
The calling code might want to keep the original values, so returning a new <code>vector</code> is better than mutating the original.
You could pass a <code>const &amp;</code> and then copy the vector yourself, but passing the parameter by value  gives you a copy automatically.</p>

<p>Now call <code>test_analysis</code> from <code>main</code>.
It’s in a namespace, so you need to specify that, too.
Put the call inside <code>main</code>, at the top of <em>main.cpp</em>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">test_analysis</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO10-1" href="#callout_using_standard_library_algorithms_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="c1">//... </code><a class="co" id="co_using_standard_library_algorithms_CO10-2" href="#callout_using_standard_library_algorithms_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="c1">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO10-1" href="#co_using_standard_library_algorithms_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Calls the tests</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO10-2" href="#co_using_standard_library_algorithms_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>As before</p></dd>
</dl>

<p>Build your code again, adding <em>analysis.cpp</em> to the source files this time.
For example, with g++:</p>

<pre data-type="programlisting" data-code-language="bash">g++<code class="w"> </code>-Wall<code class="w"> </code>-std<code class="o">=</code>c++23<code class="w"> </code>analysis.cpp<code class="w"> </code>input.cpp<code class="w"> </code>main.cpp<code class="w"> </code>-o<code class="w"> </code>stock_prices<code class="w"></code></pre>

<p>When you run your code, the test will fail, because <code>remove_if</code> does not remove the elements.
You therefore see an assertion failure:</p>

<pre data-type="programlisting" data-code-language="bash">Assertion<code class="w"> </code><code class="s1">'got.size() == 1'</code><code class="w"> </code>failed.<code class="w"></code></pre>

<p>The elements you need are now at the start of the container.
You saw that your container will be the same size after you call <code>remove_if</code>.
<a href="https://en.cppreference.com/w/cpp/algorithm/remove">CppReference</a> states that a call to <code>remove</code> is typically followed by a call to <code>erase</code>, which is called the <em>erase-remove idiom</em>.
This call erases unwanted (and unspecified) values.
If you don’t erase them, you would need to keep track of the returned iterator to avoid using them. You can make <a data-type="xref" href="#remove_without_erase">Example 5-4</a> work by erasing the elements from the new end up to the old end, as shown in <a data-type="xref" href="#remove_with_erase">Example 5-5</a>.</p>
<div id="remove_with_erase" data-type="example">
<h5><span class="label">Example 5-5. </span>Removing invalid elements properly</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">new_end</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">remove_if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><code class="n">negative</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO11-1" href="#callout_using_standard_library_algorithms_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">prices</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">new_end</code><code class="p">,</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO11-2" href="#callout_using_standard_library_algorithms_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">prices</code><code class="p">;</code><code class="w">	</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="n">test_analysis</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">got</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="p">{</code><code class="mf">-1.2</code><code class="p">,</code><code class="w"> </code><code class="mf">3.5</code><code class="p">}</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mf">3.5</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO11-1" href="#co_using_standard_library_algorithms_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Stores the new end</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO11-2" href="#co_using_standard_library_algorithms_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Erases the unwanted elements</p></dd>
</dl></div>

<p>If you build and run your code now, your test will pass.</p>

<p>You have seen the newer and older ways to remove elements from a container, and you’ll practice more algorithms in the next chapter.
Now that you only have valid prices, however, you’re ready to find the average.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Finding an average with an algorithm"><div class="sect2" id="id100">
<h2>Finding an average with an algorithm</h2>

<p>You’ll add the following code to <em>analysis.cpp</em>, and you will be able to reuse it in later chapters.</p>

<p>The <em>arithmetic mean</em>, of a collection of numbers is their total divided by their count.
You can use a <code>for</code> loop to achieve this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="nf">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">sum</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">price</code><code class="o">:</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="n">sum</code><code class="w"> </code><code class="o">+=</code><code class="w"> </code><code class="n">price</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">sum</code><code class="o">/</code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">();</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>There’s nothing wrong with a <code>for</code> loop, but sometimes the algorithm version will be more efficient or deal with edge cases better.  Let’s use an algorithm instead.
Before we do, can you think of an edge case?
It’s always worth starting to think of potential issues when you write code.
Sketching them out in scenarios (or even unit tests) will remind you to deal with them.
For instance, what happens if the <code>prices</code> container is empty?
Dividing by zero is never a good idea.
You could indicate a problem by throwing an exception or using a <code>std::expected</code>, or you could return 0.0.</p>

<p>Let’s use an exception, for practice. Add a declaration for your upcoming function to <em>analysis.h</em>, inside the namespace:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="nf">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">);</code><code class="w"></code></pre>

<p>You’ll add the definition inside <em>analysis.cpp</em>, as well as a couple of tests.
An empty vector and a vector with one element will cover what you need to get started.
You can do this in stages, starting once again with seeing a test fail.
Start with a basic, but wrong, <code>average</code> function in <em>analysis.cpp</em>, inside the namespace:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="nf">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Add both tests to your <code>test_analysis</code> function.
The first test will <em>try</em> to find the average of an empty vector.
The second will assert that the average of a number is the number itself.
<a data-type="xref" href="#average_tests">Example 5-6</a> shows what you need.</p>
<div id="average_tests" data-type="example">
<h5><span class="label">Example 5-6. </span>Two new tests for average</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="nf">test_analysis</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">got</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="p">{</code><code class="mf">-1.2</code><code class="p">,</code><code class="w"> </code><code class="mf">3.5</code><code class="p">}</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mf">3.5</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">	</code><code class="k">try</code><code class="w">
</code><code class="w">	</code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">average</code><code class="p">(</code><code class="p">{</code><code class="p">}</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO12-1" href="#callout_using_standard_library_algorithms_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">	    </code><code class="n">assert</code><code class="p">(</code><code class="nb">false</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO12-2" href="#callout_using_standard_library_algorithms_CO12-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="p">}</code><code class="w">
</code><code class="w">	</code><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">exception</code><code class="w"> </code><code class="o">&amp;</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO12-3" href="#callout_using_standard_library_algorithms_CO12-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="p">{</code><code class="w">
</code><code class="w">	</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="w">	</code><code class="n">assert</code><code class="p">(</code><code class="n">average</code><code class="p">(</code><code class="p">{</code><code class="mf">1.0</code><code class="p">}</code><code class="p">)</code><code class="o">=</code><code class="o">=</code><code class="mf">1.0</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO12-4" href="#callout_using_standard_library_algorithms_CO12-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO12-1" href="#co_using_standard_library_algorithms_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Tries to find the average of an empty vector, <code>{}</code></p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO12-2" href="#co_using_standard_library_algorithms_CO12-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>This line should never be reached, since you expect an exception, so the program asserts if you get here</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO12-3" href="#co_using_standard_library_algorithms_CO12-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Catches any <code>std::exception</code>, so if this happens, the test has succeeded</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO12-4" href="#co_using_standard_library_algorithms_CO12-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Asserts that the average of <code>{1.0}</code> is 1.0</p></dd>
</dl></div>

<p>If you run this now, you will see a message like <code>Assertion <em>false</em> failed</code>.
Because your function doesn’t check the <code>prices</code>, no exception will be thrown for an empty vector.
So at the top of <code>average</code>, add a check for an <code>empty</code> vector, and have it throw a <code>std::invalid_argument</code> error with a suitable message if needed.
The <code>std::invalid_argument</code> exception lives in the <code>&lt;stdexcept&gt;</code> header, so include that too, near the top of your <em>analysis.cpp</em> file.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;stdexcept&gt;</code><code class="c1"> </code><a class="co" id="co_using_standard_library_algorithms_CO13-1" href="#callout_using_standard_library_algorithms_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="c1">//.. </code><a class="co" id="co_using_standard_library_algorithms_CO13-2" href="#callout_using_standard_library_algorithms_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="c1">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="c1">//... </code><a class="co" id="co_using_standard_library_algorithms_CO13-3" href="#callout_using_standard_library_algorithms_CO13-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="c1">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="nf">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">            </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"</code><code class="s">Prices cannot be empty</code><code class="s">"</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO13-4" href="#callout_using_standard_library_algorithms_CO13-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO13-5" href="#callout_using_standard_library_algorithms_CO13-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO13-1" href="#co_using_standard_library_algorithms_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes various exception type, such as <code>std::invalid_argument</code></p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO13-2" href="#co_using_standard_library_algorithms_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>As before</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO13-3" href="#co_using_standard_library_algorithms_CO13-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Also as before</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO13-4" href="#co_using_standard_library_algorithms_CO13-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Throws an exception if there are no values</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO13-5" href="#co_using_standard_library_algorithms_CO13-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Returns 0.0 (which isn’t quite there yet)</p></dd>
</dl>

<p>Now you will see a new message like:</p>

<pre data-type="programlisting" data-code-language="bash">Assertion<code class="w"> </code><code class="s1">'average({1.0})==1.0'</code><code class="w"> </code>failed.<code class="w"></code></pre>

<p>You have dealt with the edge case of an empty <code>vector</code>.
Now you need to implement code for actual values.
You can use the <code>accumulate</code> function from the standard library for this.
This algorithm lives in the <code>&lt;numeric&gt;</code> header.
There are two overloads.
You’ll use the first, which takes <code>begin</code> and <code>end</code> iterators and an initial value, and sums the elements and the initial value.</p>

<p>Again, the average is the sum of all the numbers, divided by the count.
You’ll use the <code>prices</code>' <code>begin</code> and <code>end</code> and provide an initial value of 0.0, using <code>double{}</code>.
Then <code>prices.size()</code> tells you how many you have.
Instead of returning 0.0, you now return:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">accumulate</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"> </code><code class="kt">double</code><code class="p">{})</code><code class="o">/</code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">();</code><code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p>You saw how to use a <code>for</code> loop to sum numbers at the start of this section. You can use <code>std::accumulate</code> instead. There’s not much difference in this case, but practicing algorithms is useful. They are often simpler to use and help you avoid common mistakes, so using them is  therefore considered a best practice.</p>
</div>

<p>If you build your code again and run it, your tests will pass.</p>

<p>You have added a little code at a time, testing at regular points.
The <em>analysis.cpp</em> file should now include several headers, two functions, and some test code, as shown in <a data-type="xref" href="#analysis_code">Example 5-7</a>.</p>
<div id="analysis_code" data-type="example">
<h5><span class="label">Example 5-7. </span>Full analysis source</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;numeric&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;stdexcept&gt;</code><code class="cp"></code>

<code class="cp">#include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="cp"></code>

<code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">new_end</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">remove_if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"> </code><code class="n">negative</code><code class="p">);</code><code class="w"></code>
<code class="w">        </code><code class="n">prices</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">new_end</code><code class="p">,</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">());</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">prices</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>

<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">())</code><code class="w"></code>
<code class="w">            </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"Prices cannot be empty"</code><code class="p">);</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">accumulate</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"></code>
<code class="w">                    </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"></code>
<code class="w">                    </code><code class="kt">double</code><code class="p">{})</code><code class="w"></code>
<code class="w">                </code><code class="o">/</code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">();</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>

<code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="n">test_analysis</code><code class="p">()</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">got</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">({</code><code class="mf">-1.2</code><code class="p">,</code><code class="w"> </code><code class="mf">3.5</code><code class="p">});</code><code class="w"></code>
<code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">.</code><code class="n">size</code><code class="p">()</code><code class="w"> </code><code class="o">==</code><code class="w"> </code><code class="mi">1</code><code class="p">);</code><code class="w"></code>
<code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">got</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">==</code><code class="w"> </code><code class="mf">3.5</code><code class="p">);</code><code class="w"></code>

<code class="w">        </code><code class="k">try</code><code class="w"></code>
<code class="w">        </code><code class="p">{</code><code class="w"></code>
<code class="w">            </code><code class="n">average</code><code class="p">({});</code><code class="w"></code>
<code class="w">            </code><code class="n">assert</code><code class="p">(</code><code class="nb">false</code><code class="p">);</code><code class="w"></code>
<code class="w">        </code><code class="p">}</code><code class="w"></code>
<code class="w">        </code><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">exception</code><code class="w"> </code><code class="o">&amp;</code><code class="p">)</code><code class="w"></code>
<code class="w">        </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="p">}</code><code class="w"></code>

<code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">average</code><code class="p">({</code><code class="mf">1.0</code><code class="p">})</code><code class="o">==</code><code class="mf">1.0</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>You can call <code>average</code> from <code>main</code> to display the average.
Don’t forget the namespace:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Average "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">average</code><code class="p">(</code><code class="n">prices</code><code class="p">)</code><code class="w">  </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code></pre>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding Algorithms in More Depth"><div class="sect1" id="id101">
<h1>Understanding Algorithms in More Depth</h1>

<p>You have used some range algorithms and some classic algorithms with <code>begin</code> and <code>end</code>.
You have also seen how you might use a loop instead, but using a raw loop can cause problems.
If you use a loop while you erase elements, what happens?</p>








<section data-type="sect2" data-pdf-bookmark="Using for loops"><div class="sect2" id="id102">
<h2>Using for loops</h2>

<p>You’ve used a range-based <code>for</code> loop a few times now. However, there’s another type of <code>for</code> loop, sometimes called a <em>C-style</em> <code>for</code> loop.
This <code>for</code> loop has three parts:</p>

<ul>
<li>
<p>A starting statement, such as an iterator at <code>begin</code>.</p>
</li>
<li>
<p>A condition to tell the loop when to stop, such as the iterator matching <code>end</code>.</p>
</li>
<li>
<p>An <em>iteration expression</em>, which is executed after the loop body: for example,  incrementing the iterator so it’s ready for the next time around the loop.</p>
</li>
</ul>

<p>To loop over the prices, you’ll use a <code>for</code> loop like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">for</code><code class="p">(</code><code class="k">auto</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO14-1" href="#callout_using_standard_library_algorithms_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">iterator</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO14-2" href="#callout_using_standard_library_algorithms_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="o">+</code><code class="o">+</code><code class="n">iterator</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO14-3" href="#callout_using_standard_library_algorithms_CO14-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO14-1" href="#co_using_standard_library_algorithms_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Starting statement</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO14-2" href="#co_using_standard_library_algorithms_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Ending condition, stopping the loop when this is false</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO14-3" href="#co_using_standard_library_algorithms_CO14-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Increments the iterator</p></dd>
</dl>

<p>Like the <code>while</code> loop you used in <a data-type="xref" href="ch04.html#chap4_while_input_main">Example 4-4</a>, and like range-based <code>for</code> loops, you put statements in the loop body <code>{}</code>.</p>

<p>Try removing the negative numbers in your loop.
If you want to try out the code, put it in <em>analysis.cpp</em>.
Notice that the <code>negative</code> function takes a <code>double</code>, rather than an iterator to a <code>double</code>.
Armed with the <code>iterator</code>, you can use the dereference operator <code>*</code>, which you met in <a data-type="xref" href="#iterators">“Using iterators in algorithms”</a>, to get the value itself.
So <code>*iterator</code> tells you the <code>double</code> in the <code>vector</code> at <code>iterator</code>.</p>

<p>If your loop tries to erase an element in a vector, what do you think will happen?</p>
<div id="eare_in_loop" data-type="example">
<h5><span class="label">Example 5-8. </span>A very bad idea</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid_badly</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">auto</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="o">+</code><code class="o">+</code><code class="n">iterator</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">negative</code><code class="p">(</code><code class="o">*</code><code class="n">iterator</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO15-1" href="#callout_using_standard_library_algorithms_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">            </code><code class="n">prices</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">iterator</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO15-2" href="#callout_using_standard_library_algorithms_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">prices</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO15-1" href="#co_using_standard_library_algorithms_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Dereferences <code>iterator</code> to get the double</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO15-2" href="#co_using_standard_library_algorithms_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Erases the element</p></dd>
</dl></div>

<p>If you try this code, you will see an error.
For example, g++ on <a href="https://godbolt.org/z/hbj3rYav1">Godbolt</a> reports:</p>

<pre data-type="programlisting" data-code-language="bash">Program<code class="w"> </code>terminated<code class="w"> </code>with<code class="w"> </code>signal:<code class="w"> </code>SIGSEGV<code class="w"></code></pre>

<p><a href="https://en.cppreference.com/w/cpp/container/vector/erase">CppReference</a> says that references to the elements at or after the point of the erasure are <em>invalidated</em>.
When you change a container’s size, the position indicated by the iterator might no longer be there.
Trying to increment an invalid iterator is a very bad idea.</p>

<p>The <code>erase</code> function actually returns an iterator, telling you the iterator following the last removed element.
You can store it with the following code:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">iterator</code><code class="p">);</code><code class="w"></code></pre>

<p>However, this doesn’t solve all your problems.</p>

<p>If the end of your range is a negative number, say <code>{1.5, 3.2, -1.5}</code>, the <code>iterator</code> will then be at the <code>end</code>. That means the loop increment will go <em>one past</em> one past the end!
This is invalid and causes undefined behavior.
You can fix this by saving the iterator after an erase and only incrementing it for non-negative numbers, as follows:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">auto</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">iterator</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><a class="co" id="co_using_standard_library_algorithms_CO16-1" href="#callout_using_standard_library_algorithms_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">negative</code><code class="p">(</code><code class="o">*</code><code class="n">iterator</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">            </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">iterator</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO16-2" href="#callout_using_standard_library_algorithms_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">else</code><code class="w">
</code><code class="w">            </code><code class="o">+</code><code class="o">+</code><code class="n">iterator</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO16-3" href="#callout_using_standard_library_algorithms_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">prices</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO16-1" href="#co_using_standard_library_algorithms_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Leaves the <code>for</code> loop’s iterator expression empty</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO16-2" href="#co_using_standard_library_algorithms_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Saves the new iterator after an erase</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO16-3" href="#co_using_standard_library_algorithms_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Increments <code>iterator</code> for non-negative values</p></dd>
</dl>

<p>The code now works.
Try it yourself or use this <a href="https://godbolt.org/z/o17xa6rx3">Godbolt</a>.
It’s true that <code>std::erase_if</code> was much less to pay attention to, but you have learned a few new things.</p>

<p>If you do use a raw <code>for</code> loop, you need to be very careful. To sum up the issues laid out here, raw loops:</p>

<ul>
<li>
<p>are (much) more error-prone</p>
</li>
<li>
<p>often require you to write (much) more code</p>
</li>
<li>
<p>take (much) more time to write, read, and understand</p>
</li>
<li>
<p>require extra testing</p>
</li>
<li>
<p>can take more time to run and therefore are more expensive</p>
</li>
</ul>

<p>Using a raw loop for an algorithm that already exists can be informative.
However, if you catch yourself writing your own sorting algorithm, see if C++ already has it, to avoid reinventing the wheel.
Its algorithms have been designed to work with any container, and almost anything you want is likely to be there for you already.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Binary Operators and Predicates"><div class="sect2" id="id103">
<h2>Binary Operators and Predicates</h2>

<p>So far, you’ve used your <code>negative</code> unary predicate a few times.
Some algorithms take <em>binary</em> predicates, which are functions that take two parameters and return a <code>bool</code>, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="nf">some_function</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">x</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">y</code><code class="p">);</code><code class="w"></code></pre>

<p>You can sort your prices, using <code>std::sort</code>, from the <code>algorithm</code> header.
Add your code in <code>main</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">sort</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">());</code><code class="w"></code>
<code class="k">for</code><code class="p">(</code><code class="k">auto</code><code class="w"> </code><code class="n">p</code><code class="o">:</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>By default, the elements are sorted in increasing order:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;4<code class="w"></code>
&gt;3<code class="w"></code>
&gt;10.2<code class="w"></code>
&gt;done<code class="w"></code>
<code class="m">3</code><code class="w"></code>
<code class="m">4</code><code class="w"></code>
<code class="m">10</code>.2<code class="w"></code></pre>

<p>The comparison takes two parameters and returns a <code>bool</code>. You can change the comparison you use, for  example to put your prices in descending order.</p>

<p>You could write a named function to decide if one number is greater than another, but you could also do that using a function object from the <code>&lt;functional&gt;</code> header.
A <em>function object</em> is a class with an operator <code>()</code>, often referred to as the <em>call operator</em>.
The <code>&lt;functional&gt;</code> header contains several function objects, including <code>std::greater</code>.
This is a template, so you can use it for any type.</p>

<p>You’ll now pass a <code>std::greater</code> function object to <code>std::sort</code>. Don’t forget to include the <code>&lt;functional&gt;</code> header:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">sort</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(),</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">greater</code><code class="p">{});</code><code class="w"></code></pre>

<p>Your prices are now sorted in descending order:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;4<code class="w"></code>
&gt;3<code class="w"></code>
&gt;10.2<code class="w"></code>
&gt;done<code class="w"></code>
<code class="m">10</code>.2<code class="w"></code>
<code class="m">4</code><code class="w"></code>
<code class="m">3</code><code class="w"></code></pre>

<p>If you want to use ranges instead, the calls are similar, but use the container rather than two iterators.
You’ll also need to use the ranges’ <code>greater</code> function object:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">sort</code><code class="p">(</code><code class="n">prices</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_standard_library_algorithms_CO17-1" href="#callout_using_standard_library_algorithms_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">sort</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">greater</code><code class="p">{</code><code class="p">}</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_standard_library_algorithms_CO17-2" href="#callout_using_standard_library_algorithms_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_standard_library_algorithms_CO17-1" href="#co_using_standard_library_algorithms_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Sorts in increasing order</p></dd>
<dt><a class="co" id="callout_using_standard_library_algorithms_CO17-2" href="#co_using_standard_library_algorithms_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Sorts in decreasing order</p></dd>
</dl>

<p>Because C++ is always evolving, you’ll frequently find that there’s more than one way to achieve what you want.
Making use of its higher-level concepts, like ranges and algorithms, often leads to shorter code and can help you avoid problems; still, it’s worth being aware of some of the alternatives, in case you come across them in older code bases.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="More on iterators"><div class="sect2" id="more_on_iterators">
<h2>More on iterators</h2>

<p>Once the prices are sorted in decreasing order, you can find the first negative value, using <code>std::ranges::find_if</code>.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">ranges</code><code class="o">::</code><code class="n">find_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"></code>
<code class="w">                             </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code></pre>

<p>You can use this iterator to make a new <code>std::vector</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">positive</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"> </code><code class="n">iterator</code><code class="p">);</code><code class="w"></code></pre>

<p>This constructs a new <code>std::vector</code> by copying the elements from <code>begin</code> up to, but not including, the first negative number.
As you saw in <a data-type="xref" href="ch04.html#initialize_brace_or_bracket">“Initialising a vector with a fixed value”</a>, you can use curly braces to give specific values, but brackets do something completely different.</p>

<p>You’ve seen a few ways to remove negative prices now.
Sorting is useful, but putting the stock prices in order means you lose information about their behavior over time.
To correct for that, you will try some more analysis in the next chapter.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id105">
<h1>Conclusion</h1>

<p>This chapter introduced some standard algorithms, but you learned other parts of C++ too.
You built a larger program, using more than one source file, and wrote your own header files.
You will be able to reuse the input and analysis source files later on in this book.
You used a namespace to structure your code.
You also defined a function <code>inline</code> in a header, and learned about C-style <code>for</code> loops.</p>

<p>You started with ranges, finding the largest and smallest values using <code>minmax</code>.
You tried the older iterator-based algorithms, too, and you used <code>begin</code> and <code>end</code> to use all the items in a container. Key takeaways include:</p>

<ul>
<li>
<p>An iterator indicates a position in a sequence.</p>
</li>
<li>
<p>The <code>end</code> is really a one-past-the-end iterator.</p>
</li>
<li>
<p>You use operator <code>*</code> to dereference an iterator, getting the value at its position.</p>
</li>
<li>
<p>Incrementing an iterator moves to the next element in a container, but you must not go beyond the <code>end</code>.</p>
</li>
</ul>

<p>You learned how to write unary and binary predicates and use them in algorithms, too. Among other things, you learned that:</p>

<ul>
<li>
<p>Predicates return a bool</p>
</li>
<li>
<p>A unary predicate takes one parameter: for example, deciding if a number is negative</p>
</li>
<li>
<p>A binary predicate takes two parameters: for example, deciding if one number is greater than another</p>
</li>
</ul>

<p>You used a named function, <code>negative</code>, and the function objects <code>std::greater</code> and <code>std::ranges::greater</code> from the <code>&lt;functional&gt;</code> header.
The next chapter will show you other ways to write predicates and other functions you can use in algorithms.</p>

<p>C++ frequently provides more than one approach.
Using algorithms can lead to neater code than C-style <code>for</code> loops, and can be less verbose than using two iterators.
Whichever approach you use, always test your code and think about possible edge cases (like if the container is empty).</p>
</div></section>
<div data-type="footnotes"><p data-type="footnote" id="id121"><sup><a href="ch05.html#id121-marker">1</a></sup> Definition from <em>C# Brain Teasers: Exercise Your Mind</em> by Steve Love, beta version, Pragmatic, forthcoming in 2025.</p></div></div></section></div></div></body></html>