<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 4. Using loops, a std::array and a std::vector"><div class="chapter" id="chapter_four">
<h1><span class="label">Chapter 4. </span>Using loops, a std::array and a std::vector</h1>

<aside data-type="sidebar" epub:type="sidebar"><div class="sidebar" id="id119">
<h1>A Note for Early Release Readers</h1>
<p>With Early Release ebooks, you get books in their earliest form—the author’s raw and unedited content as they write—so you can take advantage of these technologies long before the official release of these titles.</p>

<p>This will be the 4th chapter of the final book.</p>

<p>If you have comments about how we might improve the content and/or examples in this book, or if you notice missing material within this chapter, please reach out to the editor at <em><EMAIL></em>.</p>
</div></aside>

<p>You can create code to write output and get input.
You got one number so far.
In this chapter, I will show you how to store several numbers using <em>containers</em>.
C++ containers are class templates and algorithms for common <em>data structures</em>, which store elements.
C++ has several different types of containers, including <em>sequenced</em> containers, like an array or a vector, and <em>associative</em> containers, letting you build lookup tables.
You will see the latter types later.</p>

<p>In this chapter, you will learn how to fill and use containers.
You will also learn how to find properties of the container, such as the largest element.
You will learn other useful parts of C++ including loops and more besides.
You are going to learn lots of new C++, so pace yourself.
When I tell you about arrays, I will introduce many new features.
By the time you get to the vectors, you will find similar ideas and might be able to guess what to try.</p>

<p>By the end of this chapter you will know a lot more about C++ and be ready to write more detailed programs.
In the next chapter, you will find out how to use various algorithms from the standard library to analyze elements in containers.</p>






<section data-type="sect1" data-pdf-bookmark="Input of several numbers using a loop"><div class="sect1" id="id80">
<h1>Input of several numbers using a loop</h1>

<p>In <a data-type="xref" href="ch02.html#chapter_two">Chapter 2</a>, you input a single number.
Let’s think about getting several numbers.
How do you get more than one number?
You could build on the <a href="ch02.html#input_main_code_block">initial input code</a>, declaring two variables to get two numbers.
You can try the code in <a data-type="xref" href="#input_several_numbers">Example 4-1</a>, but what should you do if you want three or four or more numbers?</p>
<div id="input_several_numbers" data-type="example">
<h5><span class="label">Example 4-1. </span>Attempt to input several numbers (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number1</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number2</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;&gt;</code><code class="w"> </code><code class="n">number1</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number1</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;&gt;</code><code class="w"> </code><code class="n">number2</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number2</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>If you tried this code and entered two numbers, you should see them printed.
As you learnt in <a data-type="xref" href="ch02.html#chapter_two">Chapter 2</a>, any non-numeric input causes an error.
The numbers are intialized to 0, so you will see 0s output after an error.
This code is repetitive and it is hard to think of good variable names.
I hope you agree this is not a sensible approach.</p>








<section data-type="sect2" data-pdf-bookmark="A while loop"><div class="sect2" id="id81">
<h2>A <code>while</code> loop</h2>

<p>In the last chapter you wrote <a href="ch03.html#returning_and_using_expected">code</a> to get one number and handle errors.
Let’s extend it to get a few numbers.
Start a new source file, called while_input.cpp, and use the <code>get_number</code> function from the last chapter, giving <a data-type="xref" href="#chap4_while_input">Example 4-2</a>.</p>
<div id="chap4_while_input" data-type="example">
<h5><span class="label">Example 4-2. </span>Function to get a number as before</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="c1"> </code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;string&gt;</code><code class="cp"></code>

<code class="n">std</code><code class="o">::</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w"> </code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"That's not a number"</code><code class="p">};</code><code class="w"> </code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>The <code>main</code> function will be similar, getting a number from <code>std::cin</code>.</p>
<div data-type="example">
<h5><span class="label">Example 4-3. </span>Calling the function to get a number (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>This won’t show you anything yet.
However, now you are ready to get several numbers.</p>

<p>C++, like most languages, has ways to <em>loop</em> doing the same thing over and over until a condition is met.
We can use a <code>while</code> loop here, which repeats code while a condition is true.
The <code>number</code> is a <code>std::expected</code>, and you used its <code>has_value</code> to see if it has a value.
That gives you a condition to check in a while loop.
You are going to get a <code>std::expected</code>, and check if it has a value.
If it does, you will display the number and get another value.
And so on.
Until you don’t get a value, as shown in <a data-type="xref" href="#fig_4_1">Figure 4-1</a>.</p>

<figure><div id="fig_4_1" class="figure">
<img src="assets/fig_4_1.png" alt="Looping while a condition is true" width="1280" height="720"/>
<h6><span class="label">Figure 4-1. </span>Looping while a condition is true.</h6>
</div></figure>

<p>You put the condition in brackets, as you have done with an if, for example <code>if(input_stream)</code>.
The code you want to repeat goes in curly braces, also as you did with the <code>if</code>.
Write the extra code in main, as shown in <a data-type="xref" href="#chap4_while_input_main">Example 4-4</a>.</p>
<div id="chap4_while_input_main" data-type="example">
<h5><span class="label">Example 4-4. </span>Getting a number in a loop</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO1-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO1-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">error</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO1-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO1-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO1-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Checks if the number has a value</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO1-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Runs code in the block while the condition is true</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO1-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO1-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Gets here when the condition is false</p></dd>
</dl></div>

<p>Save, build and try your program.</p>

<p>You will be able to enter numbers for as long as you want.
Your program stops when you try something non-numeric, like “bye” for example:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number.<code class="w"></code>
&gt;4<code class="w"></code>
Got<code class="w"> </code><code class="m">4</code><code class="w"> </code>thanks!<code class="w"></code>
&gt;3<code class="w"></code>
Got<code class="w"> </code><code class="m">3</code><code class="w"> </code>thanks!<code class="w"></code>
&gt;bye<code class="w"></code>
That<code class="err">'</code>s<code class="w"> </code>not<code class="w"> </code>a<code class="w"> </code>number<code class="w"></code></pre>

<p>Well done. You can now get lots of numbers.
Your code does forget them almost instantly though.
If you want to keep track, you need some more C++ knowledge.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Using an array"><div class="sect1" id="id82">
<h1>Using an array</h1>

<p>We can use a container from the standard library to hold several elements.
There are a few different containers.
I will show you the <code>std::array</code> first.</p>

<p>An <em>array</em> is a class template. so can be used to store (almost) any type.
You also specify how many items it will hold.
In <a data-type="xref" href="ch02.html#chapter_two">Chapter 2</a> you used <code>std::numeric_limits</code>, another class template.
You can put types in the angle brackets, as you did back then (<code>std::numeric_limits&lt;std::streamsize&gt;</code>).
You can also use numbers, as well as a few things we haven’t met yet. These are referred to as <em>non-type template parameters</em>.
We are collecting <code>doubles</code>, so tell the <code>std::array</code> to use <code>double</code> for the first parameter.
How many do we need?
That’s not immediately obvious.
An array needs a fixed size.
The elements form a <em>contiguous</em> block with a tiny space for housekeeping, where the size is kept, as shown in <a data-type="xref" href="#fig_4_2">Figure 4-2</a>.</p>

<figure><div id="fig_4_2" class="figure">
<img src="assets/fig_4_2.png" alt="An array of five doubles" width="697" height="317"/>
<h6><span class="label">Figure 4-2. </span>An array of five doubles.</h6>
</div></figure>

<p>Let’s try five.
We can put the type then the size we require in angle brackets like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="mi">5</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{};</code><code class="w"></code></pre>

<p>If you provide a few elements, the compiler can work out what type to use and how many you have, since C++17. This is called <em>class template argument deduction (CTAD)</em>.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">array</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mf">0.0</code><code class="p">,</code><code class="w"> </code><code class="mf">1.1</code><code class="p">,</code><code class="w"> </code><code class="mf">2.2</code><code class="p">,</code><code class="w"> </code><code class="mf">3.3</code><code class="p">,</code><code class="w"> </code><code class="mf">4.4</code><code class="p">,</code><code class="w"> </code><code class="mf">5.5</code><code class="p">};</code><code class="w"></code></pre>

<p>Create a new source file, called array_input.cpp.
Start with a short function to experiment with arrays, called <code>array_experiment</code> and include the <code>&lt;array&gt;</code> header.
Call it from <code>main</code>, ready to add details.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;array&gt;</code><code class="cp"></code>
<code class="kt">void</code><code class="w"> </code><code class="nf">array_experiment</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">array_experiment</code><code class="p">();</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>You access an element using operator <code>[]</code>, specifying the <em>index</em> or position you need.
The first element is at index 0, so for five elements, the last item is at 4, as shown in <a data-type="xref" href="#fig_4_3">Figure 4-3</a>.</p>

<figure><div id="fig_4_3" class="figure">
<img src="assets/fig_4_3.png" alt="Indexing an array of five doubles" width="685" height="378"/>
<h6><span class="label">Figure 4-3. </span>Indexing an array of five doubles.</h6>
</div></figure>

<p>Add the function in <a data-type="xref" href="#Experiment_with_array">Example 4-5</a> and call it from <code>main</code>.
What will the first line of output give?
Try it if you’re not sure.</p>
<div id="Experiment_with_array" data-type="example">
<h5><span class="label">Example 4-5. </span>Experiment with <code>std::array</code></h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;array&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="kt">void</code><code class="w"> </code><code class="nf">array_experiment</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="mi">5</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO2-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO2-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">2.5</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO2-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO2-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO2-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>declares an array of five doubles</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO2-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>displays the first value</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO2-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>changes the first value</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO2-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>displays the updated value</p></dd>
</dl></div>

<p>If you run your program, you will see the original value, 0, followed by the updated value, 2.5.</p>

<pre data-type="programlisting" data-code-language="bash"><code class="m">0</code><code class="w"></code>
<code class="m">2</code>.5<code class="w"></code></pre>

<p>The <code>numbers</code> all start at zero, but we <em>assigned</em> the new value of 2.5 to the first element, using its <em>index</em> zero.</p>
<div data-type="tip"><h6>Tip</h6>
<p>You don’t need to add <code>{}</code> to initialize the <code>std::array</code>, because it will initialize the elements for you, but it’s worth sticking with the habit of initializing your variables.
You can put some values in the braces if you want to start with specific values, like <code>{1, 2, 3, 4, 5}</code>.</p>
</div>

<p>OK, we are trying to get lots of numbers.
You can write a function based on the <code>get_number</code> function from <a data-type="xref" href="#chap4_while_input">Example 4-2</a>.
Previously, you stopped when you got non-numeric input.
If something goes wrong now, you need to clear problems and ignore non-numeric input, otherwise your program will get stuck on problem input.
You saw how to tidy up using <code>clear</code> and <code>ignore</code> in <a data-type="xref" href="ch02.html#chapter_two">Chapter 2</a>.</p>

<p>Write this version of <code>get_number</code> above <code>main</code> in your array_input.cpp file, using <a data-type="xref" href="#chap4_clear_bad_input">Example 4-6</a>.</p>
<div id="chap4_clear_bad_input" data-type="example">
<h5><span class="label">Example 4-6. </span>Function to get a number, clearing up if there is a problem</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO3-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO3-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">streamsize</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w">
</code><code class="w">	</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w">
</code><code class="w">    </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"</code><code class="s">That's not a number</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO3-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>clears errors</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO3-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>mops up unused input</p></dd>
</dl></div>

<p>In <a data-type="xref" href="#chap4_while_input_main">Example 4-4</a>, you got numbers in a <code>while</code> loop, but didn’t save them.
You can do something similar here, storing the numbers in an <code>array</code>.
The overall code, without details, is a <code>while</code> loop getting numbers as shown in <a data-type="xref" href="#fig_4_4">Figure 4-4</a>.</p>

<figure><div id="fig_4_4" class="figure">
<img src="assets/fig_4_4.png" alt="Filling an array of five doubles" width="1280" height="720"/>
<h6><span class="label">Figure 4-4. </span>Filling an array of five doubles.</h6>
</div></figure>

<p>You need to learn four more C++ fundamentals to get this working, two for the <code>while</code> condition and two to put a number in the array.
I’ll talk you through these, then you can try the new ideas in your main function, in <a data-type="xref" href="#chap4_main_array">Example 4-7</a>.</p>

<p>Now you only have a fixed amount of space in the <code>std::array</code> you are using.
You can call its <code>size</code> function to find out how much space.
If you count how many numbers you get, you can stop before you run out of space in the <code>std::array</code>.
What type should you use for this count?</p>

<p>You have met the numeric types <code>int</code> and <code>double</code> so far.
These support negative numbers, but an array, along with other containers, uses an <code>unsigned</code> number for its size.
Unsigned numbers include zero and positive numbers, and can be represented with  a <code>size_t</code>.
You can add a single <code>u</code> to the end when you declare such a value to be precise, saying like this <code>size_t count = 0u;</code>.</p>

<p>You can compare <code>count</code> with the array’s <code>size</code> in a while loop, seeing if you have fewer numbers than needed.
You want to keep looping if <code>count</code> is less than (<code>&lt;</code>) the <code>size</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">while</code><code class="p">(</code><code class="n">count</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">size</code><code class="p">())</code><code class="w"></code></pre>

<p>You are half way.
I told you about unsigned numbers and less than.
Now you need to know how to put numbers in the array.</p>

<p>You use operator <code>[]</code> to get or set an array element, using a position or index. Look back at <a data-type="xref" href="#fig_4_3">Figure 4-3</a> for a visual reminder.
In code, you put the position or index in the square brackets to access an element.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">numbers</code><code class="p">[</code><code class="n">count</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">508</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO4-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="kt">double</code><code class="w"> </code><code class="n">some_other_number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">numbers</code><code class="p">[</code><code class="n">count</code><code class="p">]</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO4-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO4-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Sets an element</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO4-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Gets an element</p></dd>
</dl>

<p>Lastly, you then need to increase the <code>count</code>, ready for the next time around the loop.
If you don’t change <code>count</code> you will overwrite the value at that position in <code>numbers</code>.
A succinct way to add one to a number uses the increment operator, <code>++</code>.</p>
<div data-type="tip" id="increment_tip"><h1>Increment operators</h1>
<p>You can add one to a value using <code>count = count + 1</code>, but you can also say <code>++count</code> or <code>count++</code>. Putting the plus signs first is the <em>preincrement</em> operator.
Consider a value</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">0</code><code class="p">;</code><code class="w"></code></pre>

<p>If you use preincrement on <code>x</code> and set a new value, <code>y</code> to x like this</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">y</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="o">++</code><code class="n">x</code><code class="p">;</code><code class="w"></code></pre>

<p><code>y</code> is set to 1, because <code>x</code> is incremented first.
Both <code>x</code> and <code>y</code> become 1.</p>

<p>Putting the sign second is the <em>postincrement</em> operator.
If you use postincrement like this</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">y</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">x</code><code class="o">++</code><code class="p">;</code><code class="w"></code></pre>

<p><code>y</code> is set to 0, the current value of <code>x</code>, first then <code>x</code> is incremented.
So, <code>x</code> is 1 and <code>y</code> is 0.</p>

<p>There are a few cases where using preincrement rather than postincrement makes a difference, but I will avoid them in this book. They can be difficult to reason about.</p>
</div>

<p>You now know four new C++ features you need to get and store several numbers.</p>
<ol>
<li>
<p>0u is an <em>unsigned</em> whole number</p>
</li>
<li>
<p>You can compare an unsigned number with an array’s <code>size</code>, for example using less than <code>&lt;</code></p>
</li>
<li>
<p>You use operator <code>[]</code> to access a specific element in an array</p>
</li>
<li>
<p>You can add 1 to a number in various ways, but using <code>++</code> to preincrement is common</p>
</li>

</ol>

<p>Using these new concepts, means you can now call your <code>get_number</code> function in a loop in the <code>main</code> function in array_input.cpp, as shown in <a data-type="xref" href="#chap4_main_array">Example 4-7</a>.</p>
<div id="chap4_main_array" data-type="example">
<h5><span class="label">Example 4-7. </span>Main code to get up to five numbers and remember them</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="mi">5u</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO5-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">size_t</code><code class="w"> </code><code class="n">count</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">0u</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO5-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">count</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO5-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">numbers</code><code class="p">[</code><code class="n">count</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO5-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO5-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="k">else</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">error</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="o">+</code><code class="o">+</code><code class="n">count</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO5-5" href="#callout_using_loops__a_std__array_and_a_std__vector_CO5-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO5-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Declares an array of five doubles</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO5-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Starts a count at zero</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO5-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Loops while we haven’t entered five things</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO5-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO5-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Stores a number at position <code>count</code></p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO5-5" href="#co_using_loops__a_std__array_and_a_std__vector_CO5-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Increases <code>count</code> by one</p></dd>
</dl></div>

<p>Save and build your code, and try it out.
You can get away with some non-numeric input as well as numbers.</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;1<code class="w"></code>
Got<code class="w"> </code><code class="m">1</code><code class="w"> </code>thanks!<code class="w"></code>
&gt;meh<code class="w"></code>
That<code class="err">'</code>s<code class="w"> </code>not<code class="w"> </code>a<code class="w"> </code>number<code class="w"></code>
&gt;3<code class="w"></code>
Got<code class="w"> </code><code class="m">3</code><code class="w"> </code>thanks!<code class="w"></code>
&gt;4<code class="w"></code>
Got<code class="w"> </code><code class="m">4</code><code class="w"> </code>thanks!<code class="w"></code>
&gt;-889900<code class="w"></code>
Got<code class="w"> </code>-889900<code class="w"> </code>thanks!<code class="w"></code></pre>

<p>You have been to the trouble of storing the numbers, but do not do anything with them.
Let’s use the array, and learn more C++.</p>








<section data-type="sect2" data-pdf-bookmark="Displaying and using the numbers"><div class="sect2" id="id83">
<h2>Displaying and using the numbers</h2>

<p>Let’s start with a way to display the values.
C++ provides a special type of loop for containers, called a <em>range-based for loop</em>, which lets you access the elements in sequence.</p>

<p>Let’s use <code>std::cout</code> to display the <code>numbers</code>.
The range-based for loop starts with the word <code>for</code>, followed by braces.
Inside the braces you choose a name of each element, like <code>number</code>, and then add a colon and the container’s name.
You can make the elements a <code>const</code> ref, and to save remembering what the element types are, you can use <code>auto</code>, which you met in the last chapter.
The body of the for loop goes inside curly braces, giving you a block to put code in.
You can use the current element in this scope.
Printing the value out will make your program display something.</p>

<p>Try the code, shown in <a data-type="xref" href="#chap4_range_based_for_array">Example 4-8</a>.</p>
<div id="chap4_range_based_for_array" data-type="example">
<h5><span class="label">Example 4-8. </span>Populating and displaying an array</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">show_numbers</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="mi">5u</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO6-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO6-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO6-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Starts a range based for loop, using the <code>numbers</code></p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO6-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Displays the current <code>number</code></p></dd>
</dl></div>

<p>You can call this in <code>main</code>, near the bottom after you get the input.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="c1">// ... </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO7-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="c1">
</code><code class="w">    </code><code class="n">show_numbers</code><code class="p">(</code><code class="n">numbers</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO7-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO7-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO7-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Code as before</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO7-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO7-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Calls the new function to display numbers</p></dd>
</dl>

<p>Try your program again.
If you give some non-numeric input you will see a zero.</p>

<pre data-type="programlisting" data-code-language="bash"><code>Please</code><code class="w"> </code><code>enter</code><code class="w"> </code><code>some</code><code class="w"> </code><code>numbers.</code><code class="w">
</code><code>&gt;3</code><code class="w">
</code><code>Got</code><code class="w"> </code><code class="m">3</code><code class="w"> </code><code>thanks!</code><code class="w">
</code><code>&gt;4</code><code class="w">
</code><code>Got</code><code class="w"> </code><code class="m">4</code><code class="w"> </code><code>thanks!</code><code class="w">
</code><code>&gt;f</code><code class="w">
</code><code>That</code><code class="err">'</code><code>s</code><code class="w"> </code><code>not</code><code class="w"> </code><code>a</code><code class="w"> </code><code>number</code><code class="w">
</code><code>&gt;6</code><code class="w">
</code><code>Got</code><code class="w"> </code><code class="m">6</code><code class="w"> </code><code>thanks!</code><code class="w">
</code><code>&gt;7</code><code class="w">
</code><code>Got</code><code class="w"> </code><code class="m">7</code><code class="w"> </code><code>thanks!</code><code class="w">
</code><code class="m">3</code><code class="w">
</code><code class="m">4</code><code class="w">
</code><code class="m">0</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO8-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="m">6</code><code class="w">
</code><code class="m">7</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO8-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>The third entry wasn’t a number, so the array element there remains at zero.</p></dd>
</dl>

<p>You can do various things with your container of numbers and a range based for loop.
Let’s find the largest number.
If you start with the first number, <code>numbers[0]</code>, you can compare this with the other values, updating the biggest if needed.</p>
<div id="find_max_by_hand" data-type="example">
<h5><span class="label">Example 4-9. </span>Find the biggest number</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">max_number</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="mi">5u</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">biggest</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">numbers</code><code class="p">[</code><code class="mi">0</code><code class="p">]</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO9-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO9-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">number</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">biggest</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO9-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">biggest</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO9-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">The biggest number is </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">biggest</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO9-5" href="#callout_using_loops__a_std__array_and_a_std__vector_CO9-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO9-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Stores the first number</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO9-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Range based for loop</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO9-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Checks if the current <code>number</code> is bigger</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO9-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Updates the biggest if it is</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO9-5" href="#co_using_loops__a_std__array_and_a_std__vector_CO9-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Displays the biggest</p></dd>
</dl></div>

<p>Call this from main and try it out.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="c1">// ...
</code><code class="w">    </code><code class="n">show_numbers</code><code class="p">(</code><code class="n">numbers</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO10-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">max_number</code><code class="p">(</code><code class="n">numbers</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO10-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO10-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Code as before</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO10-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Calls the new function to find the biggest numbers</p></dd>
</dl>

<p>You will see the biggest number displayed.
If you are wondering if we can avoid finding the first number, then looking at it again in a loop, you are getting ahead of me.
The standard library actually has some <em>algorithms</em> you can use instead to find maximums, minimums and more besides.
I will show you some of these in the next chapter.</p>

<p>You have covered a lot of ground so far. Well done.</p>

<p>The <code>std::array</code> needs a size in advance, but we don’t always know in advance how many elements we might want to keep track of.
C++ provides another container, called a <code>std::vector</code> which grows on demand.
Let’s use the <code>std::vector</code> next.
Some of the functions, like accessing an element, are exactly like the <code>std::array</code>, so you will revise a bit while you learn even more.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Using a vector"><div class="sect1" id="id84">
<h1>Using a vector</h1>

<p>Create a new source file called vector_input.cpp for this section.
The <code>std::vector</code> lives in the <code>&lt;vector&gt;</code> header, so include that and write an empty main function.
The <code>std::vector</code> is another class template, like <code>std::array</code>.
This time, you only specify the elements’ type.
You don’t fix the size because elements can be added or removed.
Like the <code>std::array</code>, a <code>std::vector</code> has a <code>size</code> function, telling you how many elements it contains.
You can state the type in angle brackets, for example:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{};</code><code class="w"></code></pre>

<p>You can also provide elements in the curly braces.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="mi">2</code><code class="p">};</code><code class="w"></code></pre>

<p>As you saw for <code>std::array</code>, the compiler can figure out the type when you provide some elements, so you don’t always need to specifying the type:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="mi">2</code><code class="p">};</code><code class="w"></code></pre>

<p>You saw how to display all the elements in a <code>std::array</code> in <a data-type="xref" href="#chap4_range_based_for_array">Example 4-8</a>, and can use another range based for loop to display the vector.
Try the code shown in <a data-type="xref" href="#chap4_range_based_for_vector">Example 4-10</a>.</p>
<div id="chap4_range_based_for_vector" data-type="example">
<h5><span class="label">Example 4-10. </span>Populating and displaying a vector</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="c1"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO11-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="c1">  </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO11-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="kt">void</code><code class="w"> </code><code class="nf">vector_experiment</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO11-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO11-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO11-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO11-5" href="#callout_using_loops__a_std__array_and_a_std__vector_CO11-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">vector_experiment</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO11-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes iostream for output</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO11-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes the vector class template</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO11-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Puts 0 and 1 in a vector</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO11-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO11-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Starts a range based for loop, using the <code>numbers</code></p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO11-5" href="#co_using_loops__a_std__array_and_a_std__vector_CO11-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Displays the current <code>number</code></p></dd>
</dl></div>

<p>You could swap the <code>std::vector</code> in <a data-type="xref" href="#chap4_range_based_for_vector">Example 4-10</a> to a <code>std::array</code>, and get the same output.
All the containers have a common subset of functions.
This allows C++ to provide useful library features that work for any container.
I will show you some of these in the next chapter.
Each container has different extra features though, and behave in different ways.
I’ll tell you more in <a data-type="xref" href="#understanding_seq_containers">“Understanding sequential containers in more depth”</a>.
Let’s have a look at some <code>std::vector</code> specialities.</p>








<section data-type="sect2" data-pdf-bookmark="Adding more elements to a vector"><div class="sect2" id="id85">
<h2>Adding more elements to a <code>vector</code></h2>

<p>You can’t add more elements to an array.
You can change an arrays’ elements, but never change its size.
In contrast, you can add elements to a vector.
Both the array and vector’s elements have a beginning and an end, and the elements are also stored contiguously.
The exact location of a <code>std:vector</code>’s might change as you add or remove elements.</p>

<p>You don’t need to know all these details to start using a <code>std::vector</code>.
However, this does mean a <code>std::vector</code> needs more housekeeping than a <code>std::array</code>, as illustrated in <a data-type="xref" href="#fig_4_5">Figure 4-5</a>.</p>

<figure><div id="fig_4_5" class="figure">
<img src="assets/fig_4_5.png" alt="A vector with some elements" width="1280" height="720"/>
<h6><span class="label">Figure 4-5. </span>A vector with some elements</h6>
</div></figure>

<p>You can insert elements anywhere in a vector, or you can add a new element at the end.
To add to the end of a <code>std::vector</code> you can use the <code>push_back</code> function.</p>

<p>Try adding a new number before you print out the <code>numbers</code> in your <code>vector_experiment</code> function.</p>

<pre id="vector_experiment" data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">vector_experiment</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="mi">-123</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO12-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO12-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Add another element to the end</p></dd>
</dl>

<p>Try out your code.
You will see 0, 1, -123 printed on separate lines.</p>

<p>You can also add elements in-between existing items, specifying where and what.
<a data-type="xref" href="#fig_4_5">Figure 4-5</a> showed the <code>begin</code> and <code>end</code> of the vector.
All containers provide corresponding <code>begin</code> and <code>end</code> functions, which return an <em>iterator</em>.
There are various types of iterators, and I will show you more details later in <a data-type="xref" href="ch05.html#iterators">“Using iterators in algorithms”</a>.</p>

<p>Let’s write a new function called <code>vector_insert</code>, and <code>insert</code> a number at the beginning of a <code>std::vector</code>.
Insert takes a position, so use <code>begin</code> for the beginning, as shown in <a data-type="xref" href="#vector_insert">Example 4-11</a>.</p>
<div id="vector_insert" data-type="example">
<h5><span class="label">Example 4-11. </span>Inserting an item at the beginning of a vector</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">vector_insert</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">insert</code><code class="p">(</code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><code class="mi">-123</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO13-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO13-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Inserts -123 at the beginning of a <code>vector</code>.</p></dd>
</dl></div>

<p>Call this from <code>main</code> and try out your code again.
You will see -123, 0, 1 printed on separate lines.</p>

<p>You can use <code>end</code> instead, and the new number will be inserted at the end, as you saw with <code>push_back</code>.
If you want to use a different position, you can increment the <code>begin</code> to move to the second element.
You do that with the <code>++</code> operator. Look back at <a data-type="xref" href="#increment_tip">“Increment operators”</a> if you need to.
Try using this to change the middle value as shown in <a data-type="xref" href="#vector_insert_middle">Example 4-12</a>.</p>
<div id="vector_insert_middle" data-type="example">
<h5><span class="label">Example 4-12. </span>Inserting an item into the middle of a vector</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">vector_insert</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO14-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">insert</code><code class="p">(</code><code class="o">+</code><code class="o">+</code><code class="n">iterator</code><code class="p">,</code><code class="w"> </code><code class="mi">-123</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO14-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">vector_insert</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO14-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Finds the beginning of a <code>vector</code>.</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO14-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Inserts -123 in the second position, one after <code>begin</code></p></dd>
</dl></div>

<p>You can do much more with a <code>std::vector</code>, and you will over the rest of this book.</p>

<p>I’ll show you a few extra features of a vector now, before we return to our original plan to fill a container of numbers.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="A few other container functions"><div class="sect2" id="id86">
<h2>A few other container functions</h2>

<p>You can jump forward by adding to an iterator:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">iterator</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">()</code><code class="w"> </code><code class="o">+</code><code class="w"> </code><code class="mi">2</code><code class="p">;</code><code class="w"></code></pre>

<p>This allows you to move to any element.
You can also subtract to go back.</p>

<p>For the <code>std::array</code>, you access an element using operator <code>[]</code>, specifying the position you need.
The <code>std::vector</code> lets you do the same.
Again, 0 corresponds to the beginning, as you saw in <a data-type="xref" href="#fig_4_3">Figure 4-3</a>.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>Nothing stops you moving too far forwards or backwards with iterators or using an invalid index in operator <code>[]</code>.
If you end up outside the elements you have undefined behavior and bad things might happen.
You met undefined behavior in <a data-type="xref" href="ch02.html#chap_2_variables">“Declaring variables”</a>.</p>
</div>

<p>You can use the <code>at</code> function instead of using operator <code>[]</code>, which checks the position you ask for.
If you want to set the tenth element, you can use either function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">numbers</code><code class="p">[</code><code class="mi">9</code><code class="p">]</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">404</code><code class="p">;</code><code class="w"></code>
<code class="n">numbers</code><code class="p">.</code><code class="n">at</code><code class="p">(</code><code class="mi">9</code><code class="p">)</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">808</code><code class="p">;</code><code class="w"></code></pre>

<p>The <code>at</code> function will throw an exception if you step outside the elements.
What should you do with such an exception though?
If you calculated the index and got it wrong, you have a mistake in your code you need to fix.
People therefore tend to use operator <code>[]</code> instead.</p>

<p>Let’s finish off this section by returning to our original problem, getting several numbers in a container.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Getting several numbers in a vector"><div class="sect2" id="id87">
<h2>Getting several numbers in a vector</h2>

<p>You have covered a lot of ground in this chapter.
You can put some numbers in a vector using the function from <a data-type="xref" href="#chap4_clear_bad_input">Example 4-6</a>, so have half of what you need already.
Copy that function into your vector_input.cpp file, above <code>main</code>.</p>

<p>You can now delete or comment out your experiment calls in <code>main</code>, and use similar code to <a data-type="xref" href="#chap4_main_array">Example 4-7</a> to get the numbers.
This time, you will <code>push_back</code> values, until the input isn’t a number.</p>
<div id="chap_4_vector_of_numbers" data-type="example">
<h5><span class="label">Example 4-13. </span>Getting numbers in a vector</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w"> </code><code class="w">
</code><code class="w">	</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">streamsize</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w">
</code><code class="w">	</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w">
</code><code class="w">    </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"</code><code class="s">That's not a number</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">     </code><code class="c1">//vector_insert();
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO15-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO15-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO15-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">error</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="s">"</code><code class="s">You entered</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO15-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO15-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>loops while you have a value</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO15-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>pushes back the value</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO15-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>tries to get another number</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO15-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>displays the numbers</p></dd>
</dl></div>

<p>Try out your code. You can add lots of numbers now.
We will find properties of a container of numbers in the next chapter, reusing much of this code.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding sequential containers in more depth"><div class="sect1" id="understanding_seq_containers">
<h1>Understanding sequential containers in more depth</h1>

<p>You have seen how to use a <code>std::array</code> and <code>std::vector</code>.
They have many similarities.
Let’s look at initializing either in more depth, and then consider a vector in more detail.</p>








<section data-type="sect2" data-pdf-bookmark="Initialising containers with an initializer list"><div class="sect2" id="id89">
<h2>Initialising containers with an initializer list</h2>

<p>You declare a <code>vector</code> and an <code>array</code> in a similar way. You can state the type they contain, but the array needs to know how many elements in advance:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">array</code><code class="o">&lt;</code><code class="kt">int</code><code class="p">,</code><code class="w"> </code><code class="mi">3</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">;</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">more_numbers</code><code class="p">;</code><code class="w"></code></pre>

<p>You can also use CTAD, so the compiler deduces the template details for you:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">array</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="mi">4</code><code class="p">,</code><code class="w"> </code><code class="mi">-3</code><code class="p">};</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">other_numbers</code><code class="p">{</code><code class="mi">2</code><code class="p">,</code><code class="w"> </code><code class="mi">5</code><code class="p">,</code><code class="w"> </code><code class="mi">-2</code><code class="p">};</code><code class="w"></code></pre>

<p>The elements in braces used to initialize the containers are called an <em>initializer list</em>.</p>

<p>If you try to put different types in such a list, you get an error.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="mf">2.5</code><code class="p">};</code><code class="w"></code></pre>

<p>The exact words will vary between compilers, but gcc says</p>

<pre data-type="programlisting" data-code-language="bash">error:<code class="w"> </code>narrowing<code class="w"> </code>conversion<code class="w"> </code>of<code class="w"> </code><code class="s1">'2.5e+0'</code><code class="w"> </code>from<code class="w"> </code><code class="s1">'double'</code><code class="w"> </code>to<code class="w"> </code><code class="s1">'int'</code><code class="w"></code></pre>

<p>You already know <code>int</code> and <code>double</code> are different types.
An <code>int</code> will (usually) fit in a double.
If you try to fit a <code>double</code> in an <code>int</code> you would lose a decimal part, and the biggest double is bigger than an int, so you might lose more than a fraction.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO16-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">y</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO16-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO16-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>OK, converting an <code>int</code> to a <code>double</code> is safe</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO16-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Converting from <em>double</em> to <em>int</em>, possible loss of data</p></dd>
</dl>

<p>How do you create a <code>vector</code> of doubles?
You can either explicitly state you want doubles, using the angle brackets, <code>std::vector&lt;double&gt; numbers{0, 1, 2.5};</code>, or make all the numbers the same type, saying <code>std::vector numbers{0.0, 1.0, 2.5};</code>.</p>

<p>Once you have set up an array, you can only change the values it contains.
In contrast a vector can grow or shrink.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="What happens when you add to a vector"><div class="sect2" id="add_to_a_vector">
<h2>What happens when you add to a vector</h2>

<p>You used <code>push_back</code> and <code>insert</code> to add elements to a vector.
The vector has space for a few items, but might run out eventually.
When that happens, the <code>std::vector</code> <em>allocates</em> more space elsewhere, copies the existing values and then adds your new value.
The vector has a <em>capacity</em> function showing how many items it has space for.
You can ask this before and after a <code>push_back</code> call to see what happens.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">0</code><code class="p">,</code><code class="w"> </code><code class="mi">1</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO17-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Space for </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">capacity</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO17-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="mi">2</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO17-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO17-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Space for </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">capacity</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO17-4" href="#callout_using_loops__a_std__array_and_a_std__vector_CO17-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO17-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Some numbers in a vector</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO17-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Displays the capacity</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO17-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO17-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Pushes a new value to the end</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO17-4" href="#co_using_loops__a_std__array_and_a_std__vector_CO17-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Displays the capacity after the new element is added</p></dd>
</dl>

<p>When I run this, I get a capacity of 2, then 4.
C++ doesn’t dictate how many items a vector has capacity for originally, nor how many more are added when needed, so you might get different values.
A vector often doubles in size though.</p>

<p>In order to keep the elements next to each other, the vector will tidy up the old elements, moving values to a new position and adding extra capacity as shown in <a data-type="xref" href="#fig_4_6">Figure 4-6</a>.</p>

<figure><div id="fig_4_6" class="figure">
<img src="assets/fig_4_6.png" alt="Adding elements to a vector might move them all" width="1280" height="720"/>
<h6><span class="label">Figure 4-6. </span>Adding elements to a vector might move them all.</h6>
</div></figure>

<p>Keeping the elements in a contiguous block can make accessing them relatively quick.
The machine will try to predict where to get data from.
When data is contiguously, the machine can predict where to look next, speeding things up.</p>

<p>You can add items to a vector, and you can erase them too.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="What happens when you delete from a vector"><div class="sect2" id="Chap4_erase_from_vector">
<h2>What happens when you delete from a vector</h2>

<p>To remove items from a vector, you use <code>erase</code>.
You can erase a single item, using an iterator, or erase several items, using a begin iterator and one beyond the last item.</p>

<p>Try the following function, calling it from <code>main</code>.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">remove_from_vector</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="w"> </code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="mi">3</code><code class="p">,</code><code class="w"> </code><code class="mi">7</code><code class="p">,</code><code class="w"> </code><code class="mi">9</code><code class="p">,</code><code class="w"> </code><code class="mi">0</code><code class="w"> </code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO18-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO18-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">numbers</code><code class="p">.</code><code class="n">erase</code><code class="p">(</code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">+</code><code class="w"> </code><code class="mi">1</code><code class="p">,</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">begin</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">+</code><code class="w"> </code><code class="mi">2</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO18-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO18-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="w"> </code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">numbers</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO18-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Declares some numbers in a vector</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO18-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Erases the first number</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO18-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO18-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Erases two more numbers</p></dd>
</dl>

<p>You start with five items, and delete the first, leaving <code>3, 7, 9, 0</code>.
You then erase the 2nd up to, but not including the 3rd (begin + 2), leaving <code>3, 9, 0</code>.
You can actually insert several items too.
Both <code>erase</code> and <code>insert</code> provide several overloads to support these differing requirements.</p>

<p>The <code>vector</code> adds space when needed.
If you erase elements, you leave unused space,
<a data-type="xref" href="#fig_4_6">Figure 4-6</a> showed extra capacity when new items were inserted.
If you check you vectors’ capacity you will see it has space for five elements after you erased elements.
That’s not a disaster, but the vector does provide a function to reclaim this, called <code>shrink_to_fit</code>.
If you call that, you will see the capacity drops back to just three</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Capacity </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">capacity</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO19-1" href="#callout_using_loops__a_std__array_and_a_std__vector_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="n">numbers</code><code class="p">.</code><code class="n">shrink_to_fit</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO19-2" href="#callout_using_loops__a_std__array_and_a_std__vector_CO19-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Capacity after a shrink </code><code class="s">"</code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">.</code><code class="n">capacity</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_using_loops__a_std__array_and_a_std__vector_CO19-3" href="#callout_using_loops__a_std__array_and_a_std__vector_CO19-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO19-1" href="#co_using_loops__a_std__array_and_a_std__vector_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Shows a capacity of 5</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO19-2" href="#co_using_loops__a_std__array_and_a_std__vector_CO19-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Shrinks the vector to fit the number of elements</p></dd>
<dt><a class="co" id="callout_using_loops__a_std__array_and_a_std__vector_CO19-3" href="#co_using_loops__a_std__array_and_a_std__vector_CO19-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Capacity shrinks to 3</p></dd>
</dl>

<p>Let’s consider one more feature of a vector before finishing up.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Initialising a vector with a fixed value"><div class="sect2" id="initialize_brace_or_bracket">
<h2>Initialising a vector with a fixed value</h2>

<p>You can initialize an <code>array</code> and <code>vector</code> in the same way, but the <code>vector</code> supports some other approaches.</p>

<p>You can ask for a <code>count</code> elements with a specific <code>value</code>, like this</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">(</code><code class="mi">2</code><code class="p">,</code><code class="w"> </code><code class="mi">5</code><code class="p">);</code><code class="w"></code></pre>

<p>The <code>numbers</code> start with two 5s.
Notice I used brackets <code>()</code> that time.
So far you have used curly braces <code>{}</code>, providing an initializer list, like this</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="mi">2</code><code class="p">,</code><code class="w"> </code><code class="mi">5</code><code class="p">};</code><code class="w"></code></pre>

<p>These <code>numbers</code> contain a two and a five.</p>

<p>The curly braces and brackets are doing something completely different.
The curly braces tend to be used to provide a specific value or set of values, while curved brackets are almost always doing something else.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Other sequential containers"><div class="sect2" id="id93">
<h2>Other sequential containers</h2>

<p>The <code>vector</code> provides <code>push_back</code> allowing you to put an element at the end.
There is no <code>push_front</code> function, but you did use <code>insert</code> to add elements at the start.
The lack of <code>push_front</code> is a hint that such a function is inappropriate, or at least inefficient, for a <code>vector</code>.
There are other containers, including a <code>std::deques</code>, pronounced deck by many people.
This container is a <em>double ended queue</em>.
The elements are typically stored in fixed sized arrays, so adding elements at the front and back are relatively efficient.
For a vector, an <code>insert</code> needs to shunt up the subsequent elements, whereas a <code>deque</code> can add a new array at the start.
This means the <code>deque</code> needs more housekeeping, and iterating through the elements involves jumping to different blocks from time to time.</p>

<p>There are many other C++ containers. <a href="https://en.cppreference.com/w/cpp/container">CppReference</a> has details if you want to know more.
Each container is designed to support certain operations efficiently, often meaning some operations are slower too. The presence or absence of member functions provides clues about what is possible or sensible.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id94">
<h1>Conclusion</h1>

<p>You met a <code>while</code> loop and the range based for loop, along with several useful C++ features.</p>

<ul>
<li>
<p><code>size_t</code> are <em>unsigned</em> whole numbers</p>
</li>
<li>
<p>You can use a trailing <code>u</code> to specify an unsigned whole number, e.g. 0u</p>
</li>
<li>
<p>Less than is <code>&lt;</code> and greater than is <code>&gt;</code></p>
</li>
<li>
<p>You can add 1 to a number in various ways, but using <code>++</code> to preincrement is common</p>
</li>
<li>
<p>You can convert an <code>int</code> to a <code>double</code> but going the other way will generate a warning</p>
</li>
</ul>

<p>You also used array and vector, two sequential containers from the standard library.</p>

<ul>
<li>
<p>These are class templates, both using a type, but the array has a fixed size too</p>
</li>
<li>
<p>You can use an initializer list, like <code>{1, 3, 2}</code> to initialize either container</p>
</li>
<li>
<p>You can specify a count and value to initialize a <code>vector</code></p>
</li>
<li>
<p>You use operator <code>[]</code> to access a specific element in an array or vector</p>
</li>
<li>
<p>All containers have <code>begin</code> and <code>end</code> functions, which you can also use to access elements</p>
</li>
<li>
<p>You can change the value of elements in an <code>array</code> but cannot change the <code>array</code> size</p>
</li>
<li>
<p>You call <code>push_back</code> on a vector to add elements to the end, and this might have to allocate new space and copy the elements.</p>
</li>
<li>
<p>You can call <code>insert</code> to add elements elsewhere in a <code>vector</code>, which might have to allocate new space and copy the elements.</p>
</li>
<li>
<p>A <code>std::deque</code> supports <code>push_front</code> because the elements are laid out differently to a <code>vector</code>, making this operation quicker.</p>
</li>
</ul>

<p>You have met lots of new C++ in this chapter.
If you get used to using <code>std::vector</code> and <code>std::array</code> you will be able to do a lot of C++.
I recommend coming back and trying the other sequential containers another time.</p>

<p>In the next chapter you will use <code>std::vector</code> more, and see how to use some algorithms from the standard library.</p>
</div></section>
</div></section></div></div></body></html>