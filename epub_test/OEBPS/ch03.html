<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 3. Exceptions and Expectations"><div class="chapter" id="chapter_three">
<h1><span class="label">Chapter 3. </span>Exceptions and Expectations</h1>


<p>You can now write a short program, and you’ve started to think about potential problems with input. I showed you how to write a function returning a <code>bool</code> to indicate success or otherwise, but there are more refined ways of handling problems. This chapter will demonstrate two approaches: exceptions and expectations. You will learn other elements of C++, getting more practice writing functions and building a deeper understanding of block scope.</p>

<p>C++ has supported exceptions for a long time, but some people writing embedded code prefer not to use them because they have an overhead.
C++23 introduced a new type called <code>std::expected</code>, which either holds a return value or an error. I will show you both approaches in this chapter, and then you’ll be ready to build a larger program in the next chapter.</p>






<section data-type="sect1" data-pdf-bookmark="Exceptions"><div class="sect1" id="id70">
<h1>Exceptions</h1>

<p>Most languages provide a way to raise an exception if a problem happens, and provide ways to handle the situation.
As with other programming languages, if you try a statement that raises an exception, the program jumps to another location.
Being able to jump out of a function or to another place if something goes wrong might seem like magic.
The specifics of how exceptions work vary between toolchains, but jumping elsewhere needs some extra housekeeping.
You can specify a function as <code>nothrow</code> to declare that it will not throw exceptions, which allows the toolchain to avoid some overhead.</p>

<p>If you detect an exception, you also need to decide what to do about it. Sometimes doing nothing and reporting the problem to the outside world is best.
In the last chapter, we wrote a function to get a number and called it from main:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">);</code><code class="w"></code></pre>

<p>If we got <code>false</code> we simply said something went wrong.
In the next chapter, we will try to get several numbers, but what will we do if a problem happens?
We could stop the entire program, but then the user will have to re-input everything.
If the data is coming over the wire, or from a file or another process, it might be OK to halt and report an error.
Someone can then fix the underlying problem and resend the data.
Error handling always throws up different options, whether you return a bool, or throw an exception or pick a different approach.
Deciding what to do usually depends on the context.</p>

<p>Let’s stick with a program that inputs a single number in this chapter. In the next chapter we will see how to get and store several numbers.
We will use exceptions to indicate problems in this section. In the next section we will learn a different approach.</p>

<p>An exception is an event that indicates a problem at runtime, stopping the normal execution or flow of the program.
There are three parts to exceptions. First, code can <em>throw</em> an exception.
You can think of that like throwing a ball, as shown in <a data-type="xref" href="#fig_3_0">Figure 3-1</a>.</p>

<figure><div id="fig_3_0" class="figure">
<img src="assets/fig_3_0.png" alt="Throwing an exception" width="1280" height="720"/>
<h6><span class="label">Figure 3-1. </span>Throwing an exception.</h6>
</div></figure>

<p>You throw to indicate a problem.
Second, you may have guessed this, calling code can <em>catch</em> an exception. Thirdly, if calling code knows a function might throw it will <em>try</em> to call the code. The <code>try</code> and <code>catch</code> go together in the calling code.
They do not need to be in the same function, but we will do that first and dig deeper later on.
The <code>throw</code> happens in the code that is called.
<a data-type="xref" href="#fig_3_1">Figure 3-2</a> gives you an overview of the <em>try</em> calling a function, and the <em>throw</em> jumping back to a <em>catch</em> block.</p>

<figure><div id="fig_3_1" class="figure">
<img src="assets/fig_3_1.png" alt="Trying, throwing and catching exceptions" width="1280" height="720"/>
<h6><span class="label">Figure 3-2. </span>Trying, throwing and catching exceptions.</h6>
</div></figure>

<p>In the last chapter, we got a number and responded depending on the <code>bool OK</code> that was returned:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">);</code><code class="w"></code>
<code class="k">if</code><code class="p">(</code><code class="n">OK</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Got "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">", thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code>
<code class="k">else</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Let’s use exceptions instead of a <code>bool</code>.
Create a new file called input_with_exception.cpp, and write an empty <code>main</code> function, which you can probably do on autopilot by now.
Look back at an example in <a href="ch02.html#chap_2_main_calling_tests_and_function">the last chapter</a> if you need to.</p>

<p>Our last <code>get_number</code> had this signature:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[[</code><code class="n">nodiscard</code><code class="p">]]</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">);</code><code class="w"></code></pre>

<p>Without the bool, we can return the <code>number</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">);</code><code class="w"></code></pre>

<p>You could argue about the <code>nodiscard</code> attribute. If someone ignores the number they asked for, that’s a bit silly, whereas ignoring the success or failure is arguably more important.
If we throw an exception, the calling code cannot ignore the problem.
If the exception is not handled the person running the program will find out about the problem, as we will see.</p>








<section data-type="sect2" data-pdf-bookmark="Throwing exceptions"><div class="sect2" id="id71">
<h2>Throwing exceptions</h2>

<p>Let’s raise an exception if the input isn’t a number.
Based on the function in <a href="ch02.html#chap_2_main_calling_tests_and_function">the last chapter</a>, write another <code>get_number</code> function in your new source file.
As before, use a <code>double</code> to get the input and check the stream using <code>if(input_stream)</code>.</p>

<p>You can now return the number itself if everything is OK, and <code>throw</code> an exception if there is a problem.
There are many different exceptions, but the most basic lives in the <code>&lt;exception&gt;</code> header.
You don’t even need to throw an exception type.
You could throw an <code>int</code> or any other type, but doing so is unconventional and will confuse other programmers.
<a data-type="xref" href="#get_number_with_exception">Example 3-1</a> shows the new <code>get_number</code> function.</p>
<div id="get_number_with_exception" data-type="example">
<h5><span class="label">Example 3-1. </span>Throwing an exception if something goes wrong</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;exception&gt;</code><code class="c1"> </code><a class="co" id="co_exceptions_and_expectations_CO1-1" href="#callout_exceptions_and_expectations_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">double</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO1-2" href="#callout_exceptions_and_expectations_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO1-3" href="#callout_exceptions_and_expectations_CO1-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO1-4" href="#callout_exceptions_and_expectations_CO1-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">exception</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO1-5" href="#callout_exceptions_and_expectations_CO1-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO1-1" href="#co_exceptions_and_expectations_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Include the header for a <code>std::exception</code>.</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO1-2" href="#co_exceptions_and_expectations_CO1-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Declares we’ll return a double this time</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO1-3" href="#co_exceptions_and_expectations_CO1-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Declares and initializes a double</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO1-4" href="#co_exceptions_and_expectations_CO1-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Returns the number because everything is ok</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO1-5" href="#co_exceptions_and_expectations_CO1-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Throws an exception, default initialized with <code>{}</code></p></dd>
</dl></div>

<p>Call your new function from main.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Please enter a number.</code><code class="se">\n</code><code class="s">&gt;"</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Got "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Save your file, build your code and have a play.
Try some numbers, then try some garbage or other characters.
If you don’t provide a number, your program will terminate with an error message.
The exact details vary, but gcc says</p>

<pre data-type="programlisting" data-code-language="bash">Program<code class="w"> </code>stderr<code class="w"></code>
terminate<code class="w"> </code>called<code class="w"> </code>after<code class="w"> </code>throwing<code class="w"> </code>an<code class="w"> </code>instance<code class="w"> </code>of<code class="w"> </code><code class="s1">'std::exception'</code><code class="w"></code>
<code class="w">  </code>what<code class="o">()</code>:<code class="w">  </code>std::exception<code class="w"></code>
Program<code class="w"> </code>terminated<code class="w"> </code>with<code class="w"> </code>signal:<code class="w"> </code>SIGSEGV<code class="w"></code></pre>

<p>Your code used <code>throw</code>, but the error tells you more.
<em>Terminate</em> means the program stopped since it didn’t know what to do with the exception.
Some more specific exceptions have a message the <code>what</code> function reports. The basic <code>std::exception</code> you used doesn’t give details beyond its type.</p>

<p>Bear in mind I told you there are <em>three</em> elements to exceptions.
You’ve seen <code>throw</code>, so you are one third of the way there.
I warned you that anyone running a program that throws an unhandled exception will find out about the problem, because they will see errors if something goes wrong.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Trying and catching"><div class="sect2" id="id72">
<h2>Trying and catching</h2>

<p>When you experimented, you will have seen some serious sounding errors if you have non-numeric input.
When internal problems with code leakinto public view the error messages can be off-putting.
Sometimes such software failures are called  <a href="https://kevlinhenney.medium.com/faq-whats-a-kevlinhenney-cc98d172f811">“A Kevlin Henney”</a>.
Rather than leaving the scary output if something goes wrong, you can use add code at the calling site to handle exceptions.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Handling exceptions with a try/catch block"><div class="sect2" id="id73">
<h2>Handling exceptions with a try/catch block</h2>

<p>You called your function from <code>main</code></p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code></pre>

<p>If nothing goes wrong, the next line is executed.
If an exception is thrown, the program jumps to the <em>end of the current scope</em>, or the next closing brace.
The program then hunts for an appropriate <em>catch block</em>.
If the calling code doesn’t catch the exception, the program goes to the code that called the calling code.
It may go all the way back to <code>main</code> and then give up, showing the error to the user.</p>

<p>The scope of your call to <code>get_number</code> is the <code>main</code> function, but you can put code inside a smaller block too.
You split your test code into small blocks in <a href="ch02.html#chap_2_test_blocks">the last chapter</a>, and you can do the same for the function call here.</p>

<p>You can try to call a function or evaluate an expression by adding the word <code>try</code> at the start of a block surrounding the potentially throwing code.
You also need to add a <code>catch</code> block to handle exceptions, and specify which type of <code>exception</code> you want to catch.
If you want to catch more than one type, you can write a catch block for each type.
We will just deal with one type here, and consider further types later.</p>

<p><a data-type="xref" href="#get_number_with_exception">Example 3-1</a> throws a <code>std::exception</code>.
Using the same source file, add <em>exception handling</em> code to <code>main</code>, as shown in <a data-type="xref" href="#try_or_catch_exception">Example 3-2</a>.
Nothing before <code>main</code> needs changing.</p>
<div id="try_or_catch_exception" data-type="example">
<h5><span class="label">Example 3-2. </span>Trying code and catching an exception if something goes wrong</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">try</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-1" href="#callout_exceptions_and_expectations_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-2" href="#callout_exceptions_and_expectations_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-3" href="#callout_exceptions_and_expectations_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">exception</code><code class="o">&amp;</code><code class="w"> </code><code class="n">ex</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-4" href="#callout_exceptions_and_expectations_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-5" href="#callout_exceptions_and_expectations_CO2-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO2-6" href="#callout_exceptions_and_expectations_CO2-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-1" href="#co_exceptions_and_expectations_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Indicates a try block</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-2" href="#co_exceptions_and_expectations_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Opens the block’s scope</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-3" href="#co_exceptions_and_expectations_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Closes the scope</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-4" href="#co_exceptions_and_expectations_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Indicates where to jump to if a <code>std::exception</code> happens</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-5" href="#co_exceptions_and_expectations_CO2-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Opens the scope for the handling code</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO2-6" href="#co_exceptions_and_expectations_CO2-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Closes the scope</p></dd>
</dl></div>

<p>Look at the <code>catch</code> line:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">exception</code><code class="o">&amp;</code><code class="w"> </code><code class="n">ex</code><code class="p">)</code><code class="w"></code></pre>

<p>You’ve seen a <code>const</code> reference before. This means you can’t change the exception. The <code>&amp;</code> indicates <em>by reference</em>, so the code doesn’t copy the exception.
It is common to call an exception <code>ex</code>, but you can give it any name you like.</p>

<p>Type the code into your cpp file, then save, build and run your program.
Try some experiments. If you type numbers, the program says “thanks!” after reporting the number.
You can get away with something starting with a number, like <code>1a</code>, but you know that’s left input in the stream after extracting the digits.
You saw how to clear extra input <a href="ch02.html#chap_2_get_number_and_clear_problems">in the last chapter</a>.</p>

<p>If you don’t enter a number, the <code>get_number</code> function throws an exception, and the code jumps to the nearest <code>catch</code> block.
This means the line reporting the number and thanking you isn’t called.
Instead, you will see <code>"Something went wrong"</code>.
This doesn’t tell the user what is wrong, and I am sure you can think of a clearer message. However, it’s less scary than <code>Terminate</code>.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Expectations"><div class="sect1" id="id74">
<h1>Expectations</h1>

<p>Some people argue that invalid user input isn’t really <em>exceptional</em>.
If you reserve exceptions for truly exceptional circumstances, you can avoid the <code>bool</code> return using a newer C++ feature, called <code>std::expected</code>.
You use this type to hold either an <em>expected</em> value or an <em>unexpected</em> value.
Learning to use <code>std::expected</code> will teach you a few more C++ ideas, so even if you would rather use exceptions, you will know lots more C++ by the end of this section.</p>
<div data-type="tip"><h6>Tip</h6>
<p>The <code>expected</code> type is a new feature introduced in C++23, so older compilers might not support it. If you run into problems, you can try out this <a href="https://godbolt.org/z/GsrxKrohY">Godbolt</a> I made for you.</p>
</div>

<p>Create another source file called input_with_expectation.cpp, adding an empty <code>main</code> function.
Include the <code>&lt;expected&gt;</code> header at the top, giving you access to the <em>class template</em> you need for this section.
You met class templates when you learnt how to <a href="ch02.html#chap_2_clearing_input_errors">clear input errors</a>.
You will meet many more over the rest of the book.
When you cleared the input, you needed to know the maximum <code>std::streamsize</code>, and you used that type in the class template <code>std::numeric_limits</code>.
That gave you the relevant <code>max</code> function for your type.
You put the type in angle brackets, <code>std::numeric_limits&lt;std::streamsize&gt;</code> so you could call its <code>max</code> function.
When types are in angle brackets, you know you’re using a <em>template</em>.</p>

<p><code>std::expected</code> takes two types; one for your expected value and one for the unexpected value if things go wrong.
We expect, or hope, to get a <code>double</code>.
For the unexpected type, you can put a message in a <code>std::string</code> if you include the <code>&lt;string&gt;</code> header.
The new <code>get_number</code> function therefore needs to return a <code>std::expected&lt;double, std::string&gt;</code>.
If we return a <code>number</code> as before, this fits the expected <code>double</code> type, so you can copy the code of the happy path from last time.
If things go wrong, you need to explicitly return a <code>std::unexpected</code> with a suitable message.</p>

<p>The <code>main</code> function needs some changes too, since it no longer gets a <code>double</code> from the function.
You check if the <code>expected</code> has a value by calling, you guessed it, <code>has_value</code>.
If it does, the <code>value</code> function tells you the value.
If it doesn’t, you call the <code>error</code> function to find out what happened.</p>

<p>The new version of <code>get_number</code> now returns a <code>std::expected&lt;double, std::string&gt;</code>, which is a bit of a mouthful.
You can simply declare an <code>auto</code> for the type when you call your new function, rather than stating in full.</p>
<div id="returning_and_using_expected" data-type="example">
<h5><span class="label">Example 3-3. </span>Returning and using a <code>std::expected</code></h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="c1"> </code><a class="co" id="co_exceptions_and_expectations_CO3-1" href="#callout_exceptions_and_expectations_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;string&gt;</code><code class="c1"> </code><a class="co" id="co_exceptions_and_expectations_CO3-2" href="#callout_exceptions_and_expectations_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO3-3" href="#callout_exceptions_and_expectations_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"</code><code class="s">That's not a number</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO3-4" href="#callout_exceptions_and_expectations_CO3-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_exceptions_and_expectations_CO3-5" href="#callout_exceptions_and_expectations_CO3-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_exceptions_and_expectations_CO3-6" href="#callout_exceptions_and_expectations_CO3-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO3-7" href="#callout_exceptions_and_expectations_CO3-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">error</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO3-8" href="#callout_exceptions_and_expectations_CO3-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-1" href="#co_exceptions_and_expectations_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes the appropriate header for <code>std:expected</code></p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-2" href="#co_exceptions_and_expectations_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes standard strings for a message</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-3" href="#co_exceptions_and_expectations_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Starts a functions returning the class template</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-4" href="#co_exceptions_and_expectations_CO3-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Returns a message if something unexpected happens</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-5" href="#co_exceptions_and_expectations_CO3-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Gets the return value using auto to avoid spelling out the full type</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-6" href="#co_exceptions_and_expectations_CO3-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Checks if everything is OK</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-7" href="#co_exceptions_and_expectations_CO3-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a></dt>
<dd><p>Uses the value</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO3-8" href="#co_exceptions_and_expectations_CO3-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a></dt>
<dd><p>Reports the unexpected error</p></dd>
</dl></div>

<p>Being familiar with <code>std::expected</code> is useful, though many older code bases will still be using exceptions.
You now know three different ways to indicate problems</p>

<ul>
<li>
<p>returning a bool, which you could extend to return an <code>int</code> with a numeric error for any problems code as the <code>main</code> function does</p>
</li>
<li>
<p>throwing exceptions</p>
</li>
<li>
<p>using <code>std::expected</code></p>
</li>
</ul>

<p>I have shown you the basics, but there are a few more details for exceptions and expectations that are worth knowing.
Let’s dive deeper.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding Exceptions and Expectations in more Depth"><div class="sect1" id="id75">
<h1>Understanding Exceptions and Expectations in more Depth</h1>

<p>You have seen two approaches to handling problems in this chapter.
In this section, I will explain each in more detail.
Let’s start with exceptions, based on the <code>get_number</code> function from <a data-type="xref" href="#get_number_with_exception">Example 3-1</a>, using <code>main</code> from <a data-type="xref" href="#try_or_catch_exception">Example 3-2</a>.
Either add to the original file, input_with_exception.cpp, or make a copy.</p>

<p>Knowing how to throw and catch exceptions is important. Many standard library functions can throw exceptions, so you are now better prepared to use more standard C++ features.
I have only shown you the <code>std::exception</code> so far, and how to catch one exception.
Let’s explore some other exception types and see how to catch more than one type.</p>








<section data-type="sect2" data-pdf-bookmark="Other exception types"><div class="sect2" id="id76">
<h2>Other exception types</h2>

<p>The <code>std::exception</code> is one type of exception. <a href="https://en.cppreference.com/w/cpp/error/exception">CppReference</a> lists several other types, and you can even write your own.
Let’s try another type of exception.</p>

<p>Suppose we want to restrict the range of numbers a user can provide, rejecting negative numbers.
The standard library header <code>&lt;stdexcept&gt;</code> provides several exception classes, including <code>std::invalid_argument</code>, which is suitable for invalid values, negative numbers in our case.
You must provide a message when you throw an <code>invalid_argument</code>, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"Please provide a non-negative number"</code><code class="p">);</code><code class="w"></code></pre>

<p>You don’t need a new line <code>\n</code> at the end of the message.
Code catching the exception can add that, or embed the message in longer feedback.</p>

<p>Make a new source file, called exception_practice.cpp.
You need a <code>main</code> function and a <code>get_number</code> function as before.
This time, extend the <code>get_number</code> function to check that the input is non-negative.
The <code>&gt;</code> symbol means greater than, and <code>&gt;=</code> means greater than or equal to.
Throw the new exception type if needed, providing a message, as shown in <a data-type="xref" href="#chap_3_two_exceptions">Example 3-4</a>.
For now, you can copy the previous main function.</p>
<div id="chap_3_two_exceptions" data-type="example">
<h5><span class="label">Example 3-4. </span>Throwing different exception types</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;exception&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;stdexcept&gt;</code><code class="c1"> </code><a class="co" id="co_exceptions_and_expectations_CO4-1" href="#callout_exceptions_and_expectations_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="kt">double</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="w"> </code><code class="p">(</code><code class="n">number</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO4-2" href="#callout_exceptions_and_expectations_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"</code><code class="s">Please provide a non-negative number</code><code class="s">"</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO4-3" href="#callout_exceptions_and_expectations_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">exception</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO4-4" href="#callout_exceptions_and_expectations_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO4-1" href="#co_exceptions_and_expectations_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes specific standard exception types</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO4-2" href="#co_exceptions_and_expectations_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Checks if the number is valid</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO4-3" href="#co_exceptions_and_expectations_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Throws an exception with a message</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO4-4" href="#co_exceptions_and_expectations_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Throws a general exception for non-numeric input</p></dd>
</dl></div>

<p>What happens if you enter a negative number?</p>

<p>The catch block in <code>main</code> only handles a general <code>std::exception</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">exception</code><code class="o">&amp;</code><code class="w"> </code><code class="n">ex</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>The <code>std::invalid_argument</code> exception is a type of <code>std::exception</code>, so the catch block can handle it.
However, we went to the trouble to add a message and that got lost.
All exceptions have a function called <code>what</code>, which allows us to get any message.</p>

<p>If you want a new line after this, you can add the new line character <code><em>\n</em></code>.
You can use single quotes for a single character.</p>

<p>Let’s add another handler for the new type of exception.
Because the <code>std::invalid_argument</code> is more specific, we need to put that <em>before</em> the handler for <code>std::exception</code>, otherwise that will be found first.
Add a new catch block to main as shown in <a data-type="xref" href="#chap_3_handling_two_exceptions">Example 3-5</a>:</p>
<div id="chap_3_handling_two_exceptions" data-type="example">
<h5><span class="label">Example 3-5. </span>Handling different exception types</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">try</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">invalid_argument</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">ex</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO5-1" href="#callout_exceptions_and_expectations_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">ex</code><code class="p">.</code><code class="n">what</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_exceptions_and_expectations_CO5-2" href="#callout_exceptions_and_expectations_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">catch</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">exception</code><code class="o">&amp;</code><code class="w"> </code><code class="n">ex</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO5-3" href="#callout_exceptions_and_expectations_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO5-1" href="#co_exceptions_and_expectations_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Provides a handler for a more specific exception type</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO5-2" href="#co_exceptions_and_expectations_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Uses the <code>what</code> function to find the message</p></dd>
<dt><a class="co" id="callout_exceptions_and_expectations_CO5-3" href="#co_exceptions_and_expectations_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Catches any other exceptions</p></dd>
</dl></div>

<p>Try this version out and some non-digits will still say <code>"Something went wrong"</code>, but a negative number now reports <code>"Please provide a non-negative number"</code>.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Position of catch blocks"><div class="sect2" id="id77">
<h2>Position of catch blocks</h2>

<p>You’ve seen what happens if you throw without a catch block.
The problem falls out of <code>main</code> and reports terminal messages to the screen.
When you put a try/catch block around the call to <code>get_number</code> you can deal with the exception.
When an exception is raised, the program <em>walks the call stack</em>, jumping back to where it came from and looking for a catch block.
If you try to call a function — let’s call it some_fn — from <code>main</code>, and that function calls another function, <code>fn</code>, the program builds up a call stack, keeping track of where to go back to when a function returns:</p>

<pre data-type="programlisting" data-code-language="bash"><code class="w">    </code>-&gt;<code class="w"> </code>fn<code class="o">()</code><code class="w"></code>
<code class="w">  </code>-&gt;<code class="w"> </code>some_fn<code class="o">()</code><code class="w"></code>
main<code class="o">()</code><code class="w"></code></pre>

<p>If an exception is thrown, the program walks back down the stack, looking for a catch block.
This is known as <em>stack unwinding</em>.
Any variables in blocks between the <em>throw</em> and <em>catch</em> are tidied up.
That means the program might end up further away from the call, as shown in <a data-type="xref" href="#fig_3_2">Figure 3-3</a>.</p>

<figure><div id="fig_3_2" class="figure">
<img src="assets/fig_3_2.png" alt="Catching exceptions from further away" width="1280" height="720"/>
<h6><span class="label">Figure 3-3. </span>Catching exceptions from further away.</h6>
</div></figure>

<p>As you saw, with no catch block on the call stack, the exception is reported to the user.
Such an exception is referred to as an <em>uncaught exception</em>.
The C++ runtime detects this, and terminates the program, reporting the error.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Expected without a value"><div class="sect2" id="id78">
<h2>Expected without a <code>value</code></h2>

<p>Let’s now go back to the second approach, using expectations.
You made a source file called input_with_expectation.cpp, so either add to that or take a copy.
Instead of a <code>double</code>, this <code>get_number</code> returns a <code>std::expected&lt;double, std::string&gt;</code>.
Take a look back at <a data-type="xref" href="#returning_and_using_expected">Example 3-3</a> if you need a reminder.</p>

<p>So, what might happen if you don’t check <code>has_value</code> for a <code>std::expected</code>?
In <a data-type="xref" href="#returning_and_using_expected">Example 3-3</a> you checked whether the <code>number</code> had a value or not before using it.
You can drop that check and your code will still build:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">);</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Got "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">()</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code></pre>

<p>What happens now if you enter something non-numeric?
I made you another <a href="https://godbolt.org/z/YxqGPGPnj">Godbolt</a> if that helps, or you can try this on your machine.</p>

<p>Now the code says <code>terminate</code> again. GCC gives the following output:</p>

<pre data-type="programlisting" data-code-language="bash">Program<code class="w"> </code>returned:<code class="w"> </code><code class="m">139</code><code class="w"></code>
Program<code class="w"> </code>stderr<code class="w"></code>
terminate<code class="w"> </code>called<code class="w"> </code>after<code class="w"> </code>throwing<code class="w"> </code>an<code class="w"> </code>instance<code class="w"> </code>of<code class="w"></code>
<code class="s1">'std::bad_expected_access&lt;std::__cxx11::basic_string&lt;</code>
<code class="s1">        char, std::char_traits&lt;char&gt;, std::allocator&lt;char&gt; &gt; &gt;'</code><code class="w"></code>
<code class="w">  </code>what<code class="o">()</code>:<code class="w">  </code>bad<code class="w"> </code>access<code class="w"> </code>to<code class="w"> </code>std::expected<code class="w"> </code>without<code class="w"> </code>expected<code class="w"> </code>value<code class="w"></code>
Program<code class="w"> </code>terminated<code class="w"> </code>with<code class="w"> </code>signal:<code class="w"> </code>SIGSEGV<code class="w"></code></pre>

<p>You know <code>main</code> returns a 0 by default if everything is OK.
This output says something other than zero was returned: 139, in fact.
This is an error value.
Terminate was called, because an exception was thrown and not handled.
That is displayed on something called <code>stderr</code>. You’ve written to <code>std::cout</code> before, the character output stream.
There’s a corresponding <code>std::cerr</code>, short for character error stream, which you can use for errors.
The specific exception is <code>std::bad_expected_access</code> and you can see the angle brackets with some type details.
You don’t need to understand all of these types to get the idea that you tried to access something expected and a bad thing happened!
You can also see the <code>what</code> function giving a slightly less intimidating message.
The <em>signal</em> is a way for a program to indicate something terminal happened.
<code>SIGSEGV</code> means a <em>segmentation fault</em> or <em>violation</em>.
You tried to read something you shouldn’t have: the <code>value</code>.
There are other signals, all starting <code>SIG</code>.</p>

<p>Other toolchains handle problems differently. Visual Studio simply tells me <code>Abort</code> was called.
You don’t need to know all the possible errors off by heart, but being used to seeing a problem helps you get a feel for what might have gone wrong.</p>

<p>I presented <code>std::expected</code> as an alternative to exceptions, but as you can see you might have to deal with exceptions if you use it.
As long as you check there is a value before you try to obtain it, you are OK.</p>

<p>You checked <code>has_value</code>, but you can use a <code>std::expected</code> in a boolean context, as you did with a stream when you said <code>if(std::cin)</code>.
Instead of <code>number.has_value()</code> you can say <code>if(number)</code> in your call:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="k">if</code><code class="w"> </code><code class="p">(</code><code class="n">number</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_exceptions_and_expectations_CO6-1" href="#callout_exceptions_and_expectations_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_exceptions_and_expectations_CO6-1" href="#co_exceptions_and_expectations_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Uses a boolean conversion instead of calling <code>has_value</code></p></dd>
</dl>

<p>This is shorthand for <code>has_value</code>, so either is fine.</p>

<p>There’s lots more to <code>std::expected</code>, but you now know enough to use this feature.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id79">
<h1>Conclusion</h1>

<p>This chapter has shown you two new ways of indicating and handling problems, but allowed you to practice some more C++ and introduced a few new ideas.</p>

<ul>
<li>
<p>You now know how to try, catch and throw exceptions</p>
</li>
<li>
<p>You learnt more about scope</p>
</li>
<li>
<p>You used another class template, <code>std::expected</code>, putting types in angle brackets <code>&lt;&gt;</code></p>
</li>
<li>
<p>You used <code>auto</code> rather than spelling out a type in full</p>
</li>
<li>
<p>You initialized a <code>double</code> and an exception using curly braces <code>{}</code></p>
</li>
</ul>

<p>Now that you’re armed with some basic syntax, and you can write functions, and deal with problems, the next chapter will show you how to build a bigger program and use more of the standard library.</p>
</div></section>
</div></section></div></div></body></html>