<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="copyright-page" epub:type="copyright-page" data-pdf-bookmark="Introducing C++"><div class="preface" id="id29">
<h1>Introducing C++</h1>

<p class="author">by <span class="firstname">Frances </span> <span class="surname">Buontempo</span></p>

<p class="copyright">Copyright © 2025 Frances Buontempo. All rights reserved.</p>

<p class="printlocation">Printed in the United States of America.</p>

<p class="publisher">Published by <span class="publishername">O’Reilly Media, Inc.</span>, 1005 Gravenstein Highway North, Sebastopol, CA 95472.</p>

<p>O’Reilly books may be purchased for educational, business, or sales promotional use. Online editions are also available for most titles (<a href="http://oreilly.com">http://oreilly.com</a>). For more information, contact our corporate/institutional sales department: 800-998-9938 or <em data-type="email"><EMAIL></em>.</p>

<ul class="stafflist">
	<li><span class="staffrole">Acquisitions Editor</span><span class="staffrole">:</span> Brian Guerin</li>
	<li><span class="staffrole">Development Editor:</span> Sarah Grey</li>
	<li><span class="staffrole">Production Editor:</span> Ashley Stussy</li>
	<!--<li><span class="staffrole">Copyeditor:</span> FILL IN COPYEDITOR</li>
	<li><span class="staffrole">Proofreader:</span> FILL IN PROOFREADER</li>
	<li><span class="staffrole">Indexer:</span> FILL IN INDEXER</li>-->
	<li><span class="staffrole">Interior Designer:</span> David Futato</li>
	<li><span class="staffrole">Cover Designer:</span> Karen Montgomery</li>
	<li><span class="staffrole">Illustrator:</span> Kate Dullea</li>
</ul>

<ul class="printings">
	<li><span class="printedition">March 2026:</span> First Edition</li>
</ul>
<!--Add additional revdate spans below as needed.-->

<div>
<h1 class="revisions">Revision History for the Early Release</h1>

<ul class="releases">
	<li><span class="revdate">2024-10-30:</span> First Release</li>
	<li><span class="revdate">2024-11-15:</span> Second Release</li>
	<li><span class="revdate">2025-01-30:</span> Third Release</li>
    <li><span class="revdate">2025-03-11:</span> Fourth Release</li>
</ul>
</div>

<p class="errata">See <a href="http://oreilly.com/catalog/errata.csp?isbn=9781098178147">http://oreilly.com/catalog/errata.csp?isbn=9781098178147</a> for release details.</p>

<div class="legal">
<p>The O’Reilly logo is a registered trademark of O’Reilly Media, Inc. <em>Introducing C++</em>, the cover image, and related trade dress are trademarks of O’Reilly Media, Inc.</p>

<p>The views expressed in this work are those of the author and do not represent the publisher’s views. While the publisher and the author have used good faith efforts to ensure that the information and instructions contained in this work are accurate, the publisher and the author disclaim all responsibility for errors or omissions, including without limitation responsibility for damages resulting from the use of or reliance on this work. Use of the information and instructions contained in this work is at your own risk. If any code samples or other technology this work contains or describes is subject to open source licenses or the intellectual property rights of others, it is your responsibility to ensure that your use thereof complies with such licenses and/or rights. <!--PROD: Uncomment the following sentence if appropriate and add it to the 
        above para:--> <!--This book is not intended as [legal/medical/financial; use the appropriate
        reference] advice. Please consult a qualified professional if you 
        require [legal/medical/financial] advice.--></p>
</div>

<div class="copyright-bottom">
<p class="isbn">978-1-098-17808-6</p>

<p class="printer">[FILL IN]</p>
</div>
</div></section></div></div></body></html>