<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 6. Lambdas and the Ranges Library"><div class="chapter" id="chapter_six">
<h1><span class="label">Chapter 6. </span>Lambdas and the Ranges Library</h1>

<aside data-type="sidebar" epub:type="sidebar"><div class="sidebar" id="id122">
<h1>A Note for Early Release Readers</h1>
<p>With Early Release ebooks, you get books in their earliest form—the author’s raw and unedited content as they write—so you can take advantage of these technologies long before the official release of these titles.</p>

<p>This will be the 6th chapter of the final book.</p>

<p>If you have comments about how we might improve the content and/or examples in this book, or if you notice missing material within this chapter, please reach out to the editor at <em><EMAIL></em>.</p>
</div></aside>

<p>You used a few algorithms in the last chapter. Some take a function, and you used a handwritten <code>negative</code> function to find negative numbers. Using a named function is fine, but C++ provides alternatives.
For example, you also used function objects, including <code>std::greater</code>.
C++ gives you another way to provide functions to algorithms: using <em>lambdas</em>, or <em>anonymous</em> (unnamed) functions.
Lambdas offer a concise way to write short functions, letting you write the predicates and other functions directly in the call to the standard algorithms.</p>

<p>This chapter will show you how to write lambdas and give you further practice using algorithms.
You used some range algorithms in the last chapter.
Ranges also offer several library features, including views, so you’ll see how to use some of these. You’ll also learn in this chapter how to use lambdas for more than the standard library algorithms.</p>

<p>In the last chapter, you wrote a function called <code>get_prices</code> in <code>main</code>, in <a data-type="xref" href="ch05.html#main_using_function_in_other_file">Example 5-3</a>.
This used the <code>get_number</code> function you defined in input.cpp.
In this chapter, you will write a more general-purpose <code>get_prices</code> function, which you can use in subsequent chapters, as well as a couple of trading strategies.
They won’t make you much money, but will show you more C++.
By the end of this chapter, your analysis and input source files will be more useful, and from there, the next chapter will show you how to generate prices rather than typing them in.</p>






<section data-type="sect1" data-pdf-bookmark="Removing negative numbers using a lambda"><div class="sect1" id="id106">
<h1>Removing negative numbers using a lambda</h1>

<p>In the last chapter, you used <code>erase_if</code> to remove negative numbers:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">erased</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">erase_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">negative</code><code class="p">);</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w">  </code><code class="n">erased</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" prices below zero</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code></pre>

<p>You put the <code>negative</code> function in your <em>analysis.h</em> file:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kr">inline</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="nf">negative</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>An alternative is to use an anonymous, or <em>lambda</em>, function , directly in the <code>erase_if</code> call.
Unlike the functions you have written so far, a lambda doesn’t have a name, hence the alternative name anonymous, and its return type can be deduced from its definition.
This leaves the parameters and implementation:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"></code></pre>

<p>That’s almost a lambda. You need one more part.</p>

<p>A lambda can use variables in the surrounding scope, either by reference or by value.
If you need to use any, they go in square brackets <code>[]</code> at the start.
You don’t need any other variables to detect whether a value is negative, so leave the brackets empty:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"></code></pre>

<p>You can pass this lambda directly into <code>erase_if</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">erased</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">erase_if</code><code class="p">([](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">});</code><code class="w"></code></pre>

<p>Using the named function <code>negative</code> makes it clear what the code will do, but putting the implementation directly in the algorithm call can have advantages, too.
For example, you can see exactly what the code is doing without having to look somewhere else for a function implementation.
Lambdas are a concise way to define short functions.
For functions needing more than a couple of lines of code, prefer a named function.</p>

<p>Let’s recap.</p>

<ul>
<li>
<p>lambdas are functions, but have no name</p>
</li>
<li>
<p>the return can be deduced for you</p>
</li>
<li>
<p>lambdas start with <code>[]</code>, which indicate variables from the surrounding scope the lambda needs</p>
</li>
<li>
<p>lambdas take parameters, like named functions</p>
</li>
<li>
<p>lambdas have an implementation in <code>{}</code>, like named functions</p>
</li>
<li>
<p>lambdas are a clear and concise way to pass a function to an algorithm</p>
</li>
</ul>

<p>You can also assign a lambda to a variable and use that in <code>erase_if</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">lambda</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">erased</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">erase_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">lambda</code><code class="p">);</code><code class="w"></code></pre>
<div data-type="tip" id="auto_lambda"><h1>Each lambdas has its own unique type</h1>
<p>If you assign a lambda to a variable, use <code>auto</code>. Every lambda has its own unique type, which C++ creates for you.
You can declare two lambdas with the same implementation:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">first_lambda</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">second_lambda</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code></pre>

<p>The type of <code>first_lambda</code> and <code>second_lambda</code> are different.</p>
</div>

<p>You can call the lambda yourself, passing a parameter in brackets as you would for the <code>negative</code> function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="n">invalid</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">lambda</code><code class="p">(</code><code class="mf">-67.0</code><code class="p">);</code><code class="w"></code></pre>

<p>Create a new <em>main.cpp</em> file and try using a lambda:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">prices</code><code class="p">{</code><code class="mf">1.01</code><code class="p">,</code><code class="w"> </code><code class="mf">2.02</code><code class="p">,</code><code class="w"> </code><code class="mf">3.03</code><code class="p">,</code><code class="w"> </code><code class="mf">-4.04</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">lambda</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">){</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">erased</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">erase_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">lambda</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w">  </code><code class="n">erased</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" prices below zero</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Let’s use lambdas a bit more.</p>








<section data-type="sect2" data-pdf-bookmark="Using a lambda to vary behavior via std::function"><div class="sect2" id="id107">
<h2>Using a lambda to vary behavior via <code>std::function</code></h2>

<p>In this section, you’re going to build on the input and analysis code you started in the last chapter.
You can add to that code or start new files.
If you  want a fresh version, use <a data-type="xref" href="ch05.html#first_header_file">Example 5-1</a> and <a data-type="xref" href="ch05.html#first_separate_source_file">Example 5-2</a>.
Let’s start with input.</p>

<p>You can generalize the <code>get_prices</code> function you wrote in <a data-type="xref" href="ch05.html#main_using_function_in_other_file">Example 5-3</a>.
To refresh your memory, here is that code:</p>
<div id="get_prices_reminder" data-type="example">
<h5><span class="label">Example 6-1. </span>A reminder of the <code>get_prices</code> function</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO1-1" href="#callout_lambdas_and_the_ranges_library_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO1-1" href="#co_lambdas_and_the_ranges_library_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Adds a <em>&gt;</em> character to prompt for more input</p></dd>
</dl></div>

<p>You used a general input stream, <code>istream</code>, here, so that you could add some tests using a string stream rather than <code>std::cin</code>.
However, we didn’t add tests in the last chapter.
The function currently outputs messages to the screen, so a test would just spew messages onto the screen, which isn’t helpful.
Ideally, you only want to see <em>if</em> tests pass or fail and <em>why</em>.
Extra output is noisy and distracting.</p>

<p>You can send in a function instead of calling <code>std::cout</code> directly, using a named function or a lambda.
This function will replace the <code>std::cout</code> line, allowing you to decide whether you want to print output or do nothing.
The function doesn’t need parameters and it can return <code>void</code>.
A suitable signature would be:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">prompt</code><code class="p">();</code><code class="w"></code></pre>

<p>How do you send a function like this to <code>get_prices</code>?
In <a data-type="xref" href="#auto_lambda">“Each lambdas has its own unique type”</a>, you saw that each lambda has a unique type.
This means you can’t specify a generic type for a function parameter, suitable for any lambda.
You could use a template, but we’ll get to that in Chapter 15.
For now, you can use a class template called <code>std::function</code> from the <code>&lt;functional&gt;</code> header, which is a general-purpose way to store any function.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>A <code>std::function</code> makes a copy of the given function.
This is less efficient than using the function directly, but it’s a reasonable choice for code you will only call once or twice.
If you need something to happen thousands of times per second, though, you should investigate alternatives, like templates.</p>
</div>

<p>Use the familiar <code>&lt;&gt;</code> for a template. Add the return type, <code>void</code>, and () for parameters, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">function</code><code class="o">&lt;</code><code class="kt">void</code><code class="w"> </code><code class="p">()</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prompt</code><code class="p">;</code><code class="w"></code></pre>

<p>The <code>void ()</code> looks like the <code>prompt</code> signature, but has no name between the return and the parameters.
The new version of <code>get_prices</code> looks like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"></code>
<code class="w">                               </code><code class="n">std</code><code class="o">::</code><code class="n">function</code><code class="o">&lt;</code><code class="kt">void</code><code class="w"> </code><code class="p">()</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prompt</code><code class="p">);</code><code class="w"></code></pre>

<p>You can use a named function or a lambda for the <code>prompt</code>.
From <code>main</code>, you will prompt with a <em>&gt;</em> and use <code>std::cin</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[]</code><code class="w"> </code><code class="p">()</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'&gt;'</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">prompt</code><code class="p">);</code><code class="w"></code></pre>

<p>You can do something else from the tests: add the more general <code>get_prices</code> function in the input files.
This process has four steps:</p>
<ol>
<li>
<p>Add the declarations of <code>get_prices</code> and <code>test_input</code> to <em>input.h</em></p>
</li>
<li>
<p>Add the definition of <code>get_input</code> to <em>input.cpp</em></p>
</li>
<li>
<p>Add the tests to <em>input.cpp</em></p>
</li>
<li>
<p>Call the new function from <code>main</code></p>
</li>

</ol>

<p>Add the declaration of <code>get_prices</code> to your <em>input.h</em> file. Declare a <code>test_input</code> function, too:</p>
<div id="improved_header_file" data-type="example">
<h5><span class="label">Example 6-2. </span>Additions to input.h</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">pragma once</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;expected&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;functional&gt;</code><code class="c1"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO2-1" href="#callout_lambdas_and_the_ranges_library_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;istream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;string&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="c1"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO2-2" href="#callout_lambdas_and_the_ranges_library_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w">
</code><code class="w">		    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">function</code><code class="o">&lt;</code><code class="kt">void</code><code class="w"> </code><code class="p">(</code><code class="p">)</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prompt</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO2-3" href="#callout_lambdas_and_the_ranges_library_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="nf">test_input</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO2-4" href="#callout_lambdas_and_the_ranges_library_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code><code class="w"> </code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO2-1" href="#co_lambdas_and_the_ranges_library_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes <code>functional</code> for <code>std::function</code></p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO2-2" href="#co_lambdas_and_the_ranges_library_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes <code>std::vector</code> used in the <code>get_prices</code> function declaration</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO2-3" href="#co_lambdas_and_the_ranges_library_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Declares <code>get_prices</code> function</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO2-4" href="#co_lambdas_and_the_ranges_library_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Declares a test function</p></dd>
</dl></div>

<p>The new function goes in <em>input.cpp</em>, along with the tests.
Replace the <code>std::cout</code> lines from <a data-type="xref" href="#get_prices_reminder">Example 6-1</a> with a call to <code>prompt()</code>:</p>
<div id="improved_input_source_file" data-type="example">
<h5><span class="label">Example 6-3. </span>Additions to input.cpp</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">expected</code><code class="o">&lt;</code><code class="kt">double</code><code class="p">,</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">string</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="k">return</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w"> </code><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">streamsize</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w">
</code><code class="w">            </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w">
</code><code class="w">        </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">unexpected</code><code class="p">{</code><code class="s">"</code><code class="s">That's not a number</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">function</code><code class="o">&lt;</code><code class="kt">void</code><code class="w"> </code><code class="p">(</code><code class="p">)</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prompt</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO3-1" href="#callout_lambdas_and_the_ranges_library_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">prompt</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO3-2" href="#callout_lambdas_and_the_ranges_library_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">numbers</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">while</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">has_value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">numbers</code><code class="p">.</code><code class="n">push_back</code><code class="p">(</code><code class="n">number</code><code class="p">.</code><code class="n">value</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">            </code><code class="n">prompt</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO3-3" href="#callout_lambdas_and_the_ranges_library_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">            </code><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_number</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">numbers</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO3-1" href="#co_lambdas_and_the_ranges_library_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Defines your new <code>get_prices</code> function, taking a way to prompt</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO3-2" href="#co_lambdas_and_the_ranges_library_CO3-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Calls the prompt</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO3-3" href="#co_lambdas_and_the_ranges_library_CO3-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Calls the prompt again</p></dd>
</dl></div>

<p>Now you can add a test function.
As before, you need to include <code>&lt;cassert&gt;</code>.
What would a suitable prompt be?
You could use a lambda to write to <code>std::cout</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[]</code><code class="w"> </code><code class="p">()</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'&gt;'</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code></pre>

<p>However, you don’t need to see that in your tests.
A function that does nothing would be better.
Removing the statement between the braces leaves a lambda with no operations to perform.
Let’s call this a <em>no op</em> for short:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[]</code><code class="w"> </code><code class="p">()</code><code class="w"> </code><code class="p">{};</code><code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p><code>[](){}</code> was originally the shortest lambda you could write. You no longer need the braces for parameters () if you have none, so the shortest possible lambda is now <code>[]{}</code>.</p>
</div>

<p>As you have done before, you can use a string stream to test input.
Add a small test function after your <code>get_prices</code> function in input.cpp, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="c1"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-1" href="#callout_lambdas_and_the_ranges_library_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;sstream&gt;</code><code class="c1"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-2" href="#callout_lambdas_and_the_ranges_library_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="c1">// As before </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-3" href="#callout_lambdas_and_the_ranges_library_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="c1">
</code><code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="nf">test_input</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-4" href="#callout_lambdas_and_the_ranges_library_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">	</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">no_input</code><code class="p">{</code><code class="s">"</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-5" href="#callout_lambdas_and_the_ranges_library_CO4-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="k">auto</code><code class="w"> </code><code class="n">no_op</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="p">)</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-6" href="#callout_lambdas_and_the_ranges_library_CO4-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">get_prices</code><code class="p">(</code><code class="n">no_input</code><code class="p">,</code><code class="w"> </code><code class="n">no_op</code><code class="p">)</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-7" href="#callout_lambdas_and_the_ranges_library_CO4-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a><code class="w">
</code><code class="w">
</code><code class="w">	</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-8" href="#callout_lambdas_and_the_ranges_library_CO4-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">get_prices</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">no_op</code><code class="p">)</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO4-9" href="#callout_lambdas_and_the_ranges_library_CO4-9"><img src="assets/9.png" alt="9" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-1" href="#co_lambdas_and_the_ranges_library_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes C’s assert function</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-2" href="#co_lambdas_and_the_ranges_library_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Includes string stream</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-3" href="#co_lambdas_and_the_ranges_library_CO4-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Functions <code>get_number</code> and <code>get_prices</code> as before</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-4" href="#co_lambdas_and_the_ranges_library_CO4-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Defines some tests in a function</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-5" href="#co_lambdas_and_the_ranges_library_CO4-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Makes an empty input stream</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-6" href="#co_lambdas_and_the_ranges_library_CO4-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Defines a lambda that does nothing, to use instead of a prompt</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-7" href="#co_lambdas_and_the_ranges_library_CO4-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a></dt>
<dd><p>Checks for an empty vector</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-8" href="#co_lambdas_and_the_ranges_library_CO4-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a></dt>
<dd><p>Makes an input stream with a single digit</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO4-9" href="#co_lambdas_and_the_ranges_library_CO4-9"><img src="assets/9.png" alt="9" width="12" height="12"/></a></dt>
<dd><p>Checks for a single digit</p></dd>
</dl>

<p>The last step is calling your new function from <code>main</code>, so you can analyze some input.
If you remove your previous code from <code>main</code>, you can add new code to test your input function and then call it.
Now you’ll need to add the message “Please enter some numbers” before the call, since the prompt doesn’t include it:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">test_input</code><code class="p">();</code><code class="w"></code>

<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Please enter some numbers.</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[]</code><code class="w"> </code><code class="p">()</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'&gt;'</code><code class="p">;};</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">prompt</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Got "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">()</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">" price(s) </code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Build your code and try it.
Don’t forget to add <em>input.cpp</em> and <em>main.cpp</em> to your instructions.
If they are in different directories, include their paths.</p>

<p>When you run your code, the tests will pass and you will be prompted for numbers:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;<code class="w"></code></pre>

<p>As you have done before, you can enter a few numbers, and type something non-numeric when you are done.
Though the program seems similar when you use it, you have improved your code.
You have reused some code and made a more general function to get prices using <code>std::function</code>.
This isn’t only a better version—​it’s also easier to test.
Let’s extend the analysis code next, learning more about ranges.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Filtering out negative numbers using the ranges’ view"><div class="sect1" id="id108">
<h1>Filtering out negative numbers using the ranges’ view</h1>

<p>In this section, you will add to your <em>analysis.cpp</em> and <em>analysis.h</em> files.
If you want a fresh copy, look back at <a data-type="xref" href="ch05.html#analysis_code">Example 5-7</a> for the source file.
The header file contains an inline definition and three declarations:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#pragma once</code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp"></code>

<code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kr">inline</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="nf">negative</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">value</code><code class="w"> </code><code class="o">&lt;</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="nf">average</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="kt">void</code><code class="w"> </code><code class="nf">test_analysis</code><code class="p">();</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>You’ve learned how to remove negative numbers and return a new copy of your elements.
You can tell that from the signature:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">remove_invalid</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">prices</code><code class="p">);</code><code class="w"></code></pre>

<p>The function takes <code>prices</code> by value, so the <code>prices</code> are copied, and returns another <code>vector</code> without negative numbers.</p>

<p>You also used <code>std::erase_if</code>, which mutates the original container.
However, changing or copying the original data might not be ideal—​you might need it later on.
Furthermore, when you only want a summary statistic, like the mean, copying elements takes time and memory.
While this isn’t a problem when you’re dealing with a small amount of data, you have an alternative.</p>

<p>You’ve used a few range algorithms. There’s a ranges library too, which extends the algorithms.
The ranges library provides a <em>view</em>, which you can use to avoid copying data or changing a container’s contents.
You can also chain views together.
Like rose-tinted glasses, a view adapts what you <em>see</em>, not what you are looking at, as indicated in <a data-type="xref" href="#fig_6_1">Figure 6-1</a>.</p>

<figure><div id="fig_6_1" class="figure">
<img src="assets/fig_6_1.png" alt="A view that filters some elements, so that you only see circles" width="1280" height="720"/>
<h6><span class="label">Figure 6-1. </span>A view that filters some elements, so that you only see circles</h6>
</div></figure>

<p>You are going to filter out negative numbers, so you’ll use the view’s <code>filter</code> function.
You therefore need to include the <code>&lt;ranges&gt;</code> header.
The <code>filter</code> function takes two parameters: the first tells it what to filter, and the second shows how to filter the elements.
You can use a lambda to select non-negative numbers, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"></code>
<code class="w">                           </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&gt;=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"> </code><code class="p">);</code><code class="w"></code></pre>

<p>Add the filtered view to <code>main</code> and display what you get:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;ranges&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">test_input</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">prompt</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> price(s) </code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="s">"</code><code class="s">The following are valid:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO5-1" href="#callout_lambdas_and_the_ranges_library_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">filter</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO5-2" href="#callout_lambdas_and_the_ranges_library_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">		    </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"> </code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO5-3" href="#callout_lambdas_and_the_ranges_library_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">valid_prices</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO5-4" href="#callout_lambdas_and_the_ranges_library_CO5-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO5-1" href="#co_lambdas_and_the_ranges_library_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Displays a message to explain the output</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO5-2" href="#co_lambdas_and_the_ranges_library_CO5-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Filters prices</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO5-3" href="#co_lambdas_and_the_ranges_library_CO5-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>The lambda finds valid numbers</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO5-4" href="#co_lambdas_and_the_ranges_library_CO5-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Loops over valid prices, displaying them</p></dd>
</dl>

<p>Build and run your code, then enter a few numbers, stopping with something non-numeric.
Your program will tell you how many entries you gave and shows the ones that are valid prices:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;3.55<code class="w"></code>
&gt;-1<code class="w"></code>
&gt;9.45<code class="w"></code>
&gt;1<code class="w"></code>
&gt;stop<code class="w"></code>
Got<code class="w"> </code><code class="m">4</code><code class="w"> </code>price<code class="o">(</code>s<code class="o">)</code><code class="w"></code>
The<code class="w"> </code>following<code class="w"> </code>are<code class="w"> </code>valid:<code class="w"></code>
<code class="m">3</code>.55<code class="w"></code>
<code class="m">9</code>.45<code class="w"></code>
<code class="m">1</code><code class="w"></code></pre>

<p>You’ve done that before, but this time, you have some tests for the input.</p>

<p>Let’s do something with those filtered prices.
Include <code>"analysis.h"</code> in <code>main</code>, so you can use your <code>average</code> function.
Your function uses a <code>std::vector&lt;double&gt;</code>, so you can’t use the view directly.
Instead, you can create a vector from your view, using <code>std::ranges::to</code>.
This is a template function, so put the <code>std::vector</code> you want inside the <code>&lt;&gt;</code> and call the function using <code>()</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">ranges</code><code class="o">::</code><code class="n">to</code><code class="o">&lt;</code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&gt;</code><code class="p">(</code><code class="n">valid_prices</code><code class="p">)</code><code class="w"></code></pre>

<p>The compiler deduces that the vector is a <code>std::vector&lt;double&gt;</code> because the <code>valid_prices</code> are doubles.
Try this in your main function, after you display the valid prices:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices_as_vector</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">to</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&gt;</code><code class="p">(</code><code class="n">valid_prices</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO6-1" href="#callout_lambdas_and_the_ranges_library_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">mean</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">average</code><code class="p">(</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO6-2" href="#callout_lambdas_and_the_ranges_library_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">                    </code><code class="n">valid_prices_as_vector</code><code class="w">
</code><code class="w">                </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">with average </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">mean</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO6-1" href="#co_lambdas_and_the_ranges_library_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Creates a vector from the view</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO6-2" href="#co_lambdas_and_the_ranges_library_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Calls <code>average</code> from the <code>stock_prices</code> namespace</p></dd>
</dl>
<div data-type="tip"><h6>Tip</h6>
<p><code>std::ranges::to</code> was added in C++23. If your toolchain doesn’t support it yet, you can create the vector yourself using <code>begin</code> and <code>end</code>, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">valid_prices_as_vector</code><code class="p">{</code><code class="n">valid_prices</code><code class="p">.</code><code class="n">begin</code><code class="p">(),</code><code class="w"></code>
<code class="w">                     </code><code class="n">valid_prices</code><code class="p">.</code><code class="n">end</code><code class="p">()};</code><code class="w"></code></pre>
</div>

<p>Don’t forget to include the analysis header in <em>main.cpp</em>, and to include <em>analysis.cpp</em> in your build.
Try a few numbers and see what happens.
Here’s an example run:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;1.25<code class="w"></code>
&gt;3.25<code class="w"></code>
&gt;q<code class="w"></code>
Got<code class="w"> </code><code class="m">2</code><code class="w"> </code>price<code class="o">(</code>s<code class="o">)</code><code class="w"></code>
The<code class="w"> </code>following<code class="w"> </code>are<code class="w"> </code>valid:<code class="w"></code>
<code class="m">1</code>.25<code class="w"></code>
<code class="m">3</code>.25<code class="w"></code>
with<code class="w"> </code>average<code class="w"> </code><code class="m">2</code>.25<code class="w"></code></pre>

<p>At the moment, your <code>average</code> function takes a vector.
Constructing a vector from the view means you’ve copied elements.
(In Chapter 15, you’ll learn how to make the function more general using a template, which can use a vector or a view.)
You have learned more about ranges, so let’s try some further analysis.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Using lambda captures for fun and profit"><div class="sect1" id="fun_and_profit">
<h1>Using lambda captures for fun and profit</h1>

<p>Suppose you buy stock at the first price you see.
You can sell as soon as the price goes above that and make a profit.
This is a simplification, of course: in real life, you would be charged for this transaction.
Furthermore, the stock’s price might never go above the price you paid, so you might never make a profit.
However, you’re here to learn C++, not to make a profit, and you won’t lose any money in a simulation.</p>

<p>Let’s add a function to <em>analysis.cpp</em> and a declaration to <em>analysis.h</em>. The new function will take a <code>std::vector&lt;double&gt;</code> of prices by <code>const</code> reference and return a potential profit.
In finance jargon, when the price goes up, it is described as an <em>uptick</em>.
You want the first uptick, so add a function declaration to <em>analysis.h</em>, inside the <code>stock_prices</code> namespace:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="nf">profit_on_first_uptick</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">);</code><code class="w"></code></pre>

<p>Given some valid prices and at least one element, you can find the first element using <code>front</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">first</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">front</code><code class="p">();</code><code class="w"></code></pre>

<p>You must have some elements to do this, so check that the prices are not <code>empty</code> first.
You then have the first price.
Time to see if you can make a profit.</p>

<p>Use <code>std::ranges::find_if</code> to find if there’s a price greater than the <code>first</code>.
(You’ve used this algorithm before, in <a data-type="xref" href="ch05.html#more_on_iterators">“More on iterators”</a>.)
The <code>find_if</code> function needs a predicate.
You want to detect if any price is greater than the <code>first</code> price, because the potential profit comes when the price first goes up.
The predicate is called for each value in the container or range, so the predicate will take a double.
You can use a lambda to compare the double with the <code>first</code> value, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p>You can write the lambda on a single line, as you did before:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"></code></pre>

<p>Both versions are equivalent. Notice how a lambda looks exactly like a function, but without a name or an explicit return value.</p>
</div>

<p>Unfortunately, this lambda will not compile yet. So far, you can check that you have prices and find the <code>first</code> element with <code>front</code>.</p>

<p>Let’s look at the lambda:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"</code><code class="s">Prices cannot be empty</code><code class="s">"</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">first</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">front</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="k">auto</code><code class="w"> </code><code class="n">lambda</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO7-1" href="#callout_lambdas_and_the_ranges_library_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO7-1" href="#co_lambdas_and_the_ranges_library_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Tries to use the <code>first</code> price</p></dd>
</dl>

<p>The lambda uses <code>first</code>, but that’s declared outside the lambda, so it isn’t in scope.
To fix the problem, you need to add something <em>inside</em> the <code>[]</code>.
I mentioned earlier that a lambda can use variables in the surrounding scope, either by reference or by value.
You can put a specific variable in the braces.
The braces <em>capture</em> the variable by value, meaning that the lambda can use a copy of the variable:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="n">first</code><code class="p">](</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"></code></pre>

<p>Let’s implement the new <code>profit_on_first_uptick</code> in <em>analysis.cpp</em>.
You can put it anywhere inside the <code>stock_prices</code> namespace.</p>

<p>Next, you want to find a value greater than the <code>first</code> and report the difference.
If there is none, that means no profit.
Remember that the <code>find_if</code> algorithm returns an iterator, so you need to dereference using operator * to obtain the first profitable value.
Pulling this together gives you a new analysis function:</p>
<div id="profit_on_first_uptick" data-type="example">
<h5><span class="label">Example 6-4. </span>Function to find potential profit</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;stdexcept&gt;</code><code class="cp">
</code><code class="w">
</code><code class="k">namespace</code><code class="w"> </code><code class="nn">stock_prices</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="nf">profit_on_first_uptick</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">prices</code><code class="p">.</code><code class="n">empty</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w">
</code><code class="w">            </code><code class="k">throw</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">invalid_argument</code><code class="p">(</code><code class="s">"</code><code class="s">Prices cannot be empty</code><code class="s">"</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-1" href="#callout_lambdas_and_the_ranges_library_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">	</code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">first</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">front</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-2" href="#callout_lambdas_and_the_ranges_library_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">auto</code><code class="w"> </code><code class="n">where</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">find_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="w">
</code><code class="w">			    </code><code class="p">[</code><code class="n">first</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-3" href="#callout_lambdas_and_the_ranges_library_CO8-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">			    </code><code class="p">{</code><code class="w">
</code><code class="w">			       </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w">
</code><code class="w">			    </code><code class="p">}</code><code class="w">
</code><code class="w">			</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="n">where</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-4" href="#callout_lambdas_and_the_ranges_library_CO8-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="k">return</code><code class="w"> </code><code class="o">*</code><code class="n">where</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-5" href="#callout_lambdas_and_the_ranges_library_CO8-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">        </code><code class="k">else</code><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="k">return</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO8-6" href="#callout_lambdas_and_the_ranges_library_CO8-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-1" href="#co_lambdas_and_the_ranges_library_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Throw an exception for empty prices</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-2" href="#co_lambdas_and_the_ranges_library_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Gets the first price</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-3" href="#co_lambdas_and_the_ranges_library_CO8-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Captures <code>first</code> by value for use in the lambda</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-4" href="#co_lambdas_and_the_ranges_library_CO8-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Checks that you’re not at the <code>end</code>, because <code>end()</code> means nothing was found</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-5" href="#co_lambdas_and_the_ranges_library_CO8-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Returns the difference between the value at the found position and the first value</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO8-6" href="#co_lambdas_and_the_ranges_library_CO8-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Returns 0.0 to indicate that no profit is possible</p></dd>
</dl></div>

<p>Your new analysis function, <code>profit_on_first_uptick</code>, takes a vector, as does the <code>average</code> function.
You can call the new function in <code>main</code> using <code>valid_prices_as_vector</code>.
Add a call at the end of <code>main</code> to report the potential profit:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="n">potential_profit</code><code class="w"> </code><code class="o">=</code><code class="w"></code>
<code class="w">        </code><code class="n">stock_prices</code><code class="o">::</code><code class="n">profit_on_first_uptick</code><code class="p">(</code><code class="n">valid_prices_as_vector</code><code class="p">);</code><code class="w"></code>
<code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Potential profit "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">potential_profit</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code></pre>

<p>Build and run your program.
A sample run might look like this:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>some<code class="w"> </code>numbers.<code class="w"></code>
&gt;1.25<code class="w"></code>
&gt;0.75<code class="w"></code>
&gt;bye<code class="w"></code>
Got<code class="w"> </code><code class="m">2</code><code class="w"> </code>price<code class="o">(</code>s<code class="o">)</code><code class="w"></code>
The<code class="w"> </code>following<code class="w"> </code>are<code class="w"> </code>valid:<code class="w"></code>
<code class="m">1</code>.25<code class="w"></code>
<code class="m">0</code>.75<code class="w"></code>
with<code class="w"> </code>average<code class="w"> </code><code class="m">1</code><code class="w"></code>
Potential<code class="w"> </code>profit<code class="w"> </code><code class="m">0</code><code class="w"></code></pre>

<p>You may not have made a profit, but you have now written quite a large program.
Well done.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding Lambdas and Views in More Depth"><div class="sect1" id="chap6_in_depth">
<h1>Understanding Lambdas and Views in More Depth</h1>

<p>You’ve used algorithms a few times, and now you know how to write a lambda and use a view, but there’s a bit more you should know about lambdas and views before we move on.</p>








<section data-type="sect2" data-pdf-bookmark="Lambda captures by value"><div class="sect2" id="id111">
<h2>Lambda captures by value</h2>

<p>You’ve written a few lambdas and you’ve even used the capture <code>[]</code>, also called a <em>capture group</em>.
You’ve only captured one variable by value so far.
Let’s explore what else is possible.</p>

<p>Suppose you hold your nerve and decide to wait for a minimum profit, rather than selling as soon as the price rises above your initial investment.
The lambda in <code>profit_on_first_uptick</code> checks for a <code>price</code> greater than the <code>first</code>.
You can check for a difference greater than a required profit instead:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w"></code></pre>

<p>This tells you if you could have made the required profit from the prices.</p>

<p>Declare a new function in <em>analysis.h</em>, taking the <code>prices</code> along with a <code>required_profit</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="nf">required_profit_possible</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">,</code><code class="w"></code>
<code class="w">                              </code><code class="kt">double</code><code class="w"> </code><code class="n">required_profit</code><code class="p">);</code><code class="w"></code></pre>

<p>To implement the function, you’ll need to know how to capture the new value in the lambda.
Previously, you put one variable in the capture group:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="n">first</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO9-1" href="#callout_lambdas_and_the_ranges_library_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="w"> </code><code class="n">first</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO9-1" href="#co_lambdas_and_the_ranges_library_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Captures one variable by value in the capture group <code>[]</code>, and thus copies it</p></dd>
</dl>

<p>Now you need to capture another value.
Add  <code>required_profit</code> to the group, using a comma to separate it from  <code>first</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="n">first</code><code class="p">,</code><code class="w"> </code><code class="n">required_profit</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Pulling this together gives you the following function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="nf">required_profit_possible</code><code class="p">(</code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">prices</code><code class="p">,</code><code class="w">
</code><code class="w">                              </code><code class="kt">double</code><code class="w"> </code><code class="n">required_profit</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">first</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">front</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">where</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">find_if</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w">
</code><code class="w">                        </code><code class="p">[</code><code class="n">first</code><code class="p">,</code><code class="w"> </code><code class="n">required_profit</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO10-1" href="#callout_lambdas_and_the_ranges_library_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">                        </code><code class="p">{</code><code class="w">
</code><code class="w">                           </code><code class="k">return</code><code class="w"> </code><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w">
</code><code class="w">                        </code><code class="p">}</code><code class="w">
</code><code class="w">                    </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="n">where</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">end</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO10-2" href="#callout_lambdas_and_the_ranges_library_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO10-1" href="#co_lambdas_and_the_ranges_library_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Captures two variables by value</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO10-2" href="#co_lambdas_and_the_ranges_library_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Checks for a suitable value</p></dd>
</dl>

<p>Picking a <code>required_profit</code> and call this function from <code>main</code>. Here’s the whole listing:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;algorithm&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;ranges&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"analysis.h"</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">"input.h"</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">test_input</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter some numbers.</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prompt</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">&gt;</code><code class="sc">'</code><code class="p">;</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">get_prices</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">prompt</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">size</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s"> price(s) </code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="s">"</code><code class="s">The following are valid:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">filter</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w">
</code><code class="w">		    </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"> </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">valid_prices</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="w"> </code><code class="n">valid_prices_as_vector</code><code class="w"> </code><code class="o">=</code><code class="w">
</code><code class="w">	    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">ranges</code><code class="o">:</code><code class="o">:</code><code class="n">to</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="o">&gt;</code><code class="p">(</code><code class="n">valid_prices</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">mean</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">average</code><code class="p">(</code><code class="w">
</code><code class="w">		        </code><code class="n">valid_prices_as_vector</code><code class="w">
</code><code class="w">		    </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">with average </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">mean</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">potential_profit</code><code class="w"> </code><code class="o">=</code><code class="w">
</code><code class="w">	    </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">profit_on_first_uptick</code><code class="p">(</code><code class="n">valid_prices_as_vector</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Potential profit </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">potential_profit</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">required_profit</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">1.75</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO11-1" href="#callout_lambdas_and_the_ranges_library_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">bool</code><code class="w"> </code><code class="n">possible</code><code class="w"> </code><code class="o">=</code><code class="w">
</code><code class="w">        </code><code class="n">stock_prices</code><code class="o">:</code><code class="o">:</code><code class="n">required_profit_possible</code><code class="p">(</code><code class="n">valid_prices_as_vector</code><code class="p">,</code><code class="w">
</code><code class="w">                         </code><code class="n">required_profit</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO11-2" href="#callout_lambdas_and_the_ranges_library_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Required profit possible </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">possible</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO11-3" href="#callout_lambdas_and_the_ranges_library_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO11-1" href="#co_lambdas_and_the_ranges_library_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Picks a required profit</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO11-2" href="#co_lambdas_and_the_ranges_library_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Finds out if this profit is possible</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO11-3" href="#co_lambdas_and_the_ranges_library_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Displays the result</p></dd>
</dl>

<p>When you build and run the code now, you get an indication of whether the required profit is possible:</p>

<pre id="cout_with_bool" data-type="programlisting" data-code-language="bash"><code>Please</code><code class="w"> </code><code>enter</code><code class="w"> </code><code>some</code><code class="w"> </code><code>numbers.</code><code class="w">
</code><code>&gt;1.25</code><code class="w">
</code><code>&gt;2.13</code><code class="w">
</code><code>&gt;4.51</code><code class="w">
</code><code>&gt;bye</code><code class="w">
</code><code>Got</code><code class="w"> </code><code class="m">3</code><code class="w"> </code><code>price</code><code class="o">(</code><code>s</code><code class="o">)</code><code class="w">
</code><code>The</code><code class="w"> </code><code>following</code><code class="w"> </code><code>are</code><code class="w"> </code><code>valid:</code><code class="w">
</code><code class="m">1</code><code>.25</code><code class="w">
</code><code class="m">2</code><code>.13</code><code class="w">
</code><code class="m">4</code><code>.51</code><code class="w">
</code><code>with</code><code class="w"> </code><code>average</code><code class="w"> </code><code class="m">2</code><code>.63</code><code class="w">
</code><code>Potential</code><code class="w"> </code><code>profit</code><code class="w"> </code><code class="m">0</code><code>.88</code><code class="w">
</code><code>Required</code><code class="w"> </code><code>profit</code><code class="w"> </code><code>possible</code><code class="w"> </code><code class="m">1</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO12-1" href="#callout_lambdas_and_the_ranges_library_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO12-1" href="#co_lambdas_and_the_ranges_library_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Indicates if the profit is possible</p></dd>
</dl>

<p>Annoyingly, <code>std::cout</code> prints 1 for <code>true</code> and 0 for <code>false</code>.
To display <code>true</code> or <code>false</code> instead, you can use <code>std::boolalpha</code> from the <code>&lt;ios&gt;</code> header before you stream out the <code>bool</code>, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Required profit possible "</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">boolalpha</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">possible</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code></pre>
<div data-type="tip"><h6>Tip</h6>
<p><code>boolalpha</code> is a <em>manipulator</em> for a stream: it manipluates a bool, showing it as <code>true</code> or <code>false</code> rather than 0 or 1.
Manipulators provide options for controlling how characters are used in input and output streams, for example changing how many digits are shown after a decimal point.
In Chapter 9, you will go back to using <code>std::println</code>, which directly prints <code>true</code> or <code>false</code> for a <code>bool</code>, and provides other ways to format output, so you won’t need manipulators.</p>
</div>

<p>Now, you might want to capture several more values.
Two isn’t so many, but more will give a long list.
You can use an equals sign instead of a list to indicate that you want to capture any variable used by value:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="o">=</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>If you find yourself needing a lot of captured variables in a lambda, that might be a sign that you’re trying to do too much at once.
Use <code>=</code> sparingly to avoid capturing something by mistake.
Explicit is often better than implicit.</p>
</div>

<p>Now, you can’t change or mutate variables you’ve captured by copy.
To change them, add the word <code>mutable</code> after the parameters:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="n">first</code><code class="p">,</code><code class="w"> </code><code class="n">required_profit</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="k">mutable</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO13-1" href="#callout_lambdas_and_the_ranges_library_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">first</code><code class="w"> </code><code class="o">+</code><code class="o">=</code><code class="w"> </code><code class="mf">42.0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO13-2" href="#callout_lambdas_and_the_ranges_library_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO13-1" href="#co_lambdas_and_the_ranges_library_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Says the lambda might change or mutate a captured variable</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO13-2" href="#co_lambdas_and_the_ranges_library_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Changes <code>first</code> (which is a silly idea here!)</p></dd>
</dl>

<p>If you want to change a captured variable, you have a clearer option: you can capture values by reference instead.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Lambda captures by reference"><div class="sect2" id="id112">
<h2>Lambda captures by reference</h2>

<p>Changing the value of <code>first</code> is contrived, and not very sensible, but it does illustrate what is possible.</p>

<p>To take a capture by reference, add the reference symbol <code>&amp;</code> to that variable:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[</code><code class="o">&amp;</code><code class="n">first</code><code class="p">,</code><code class="w"> </code><code class="n">required_profit</code><code class="p">]</code><code class="w"> </code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO14-1" href="#callout_lambdas_and_the_ranges_library_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">first</code><code class="w"> </code><code class="o">+</code><code class="o">=</code><code class="w"> </code><code class="mf">42.0</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO14-2" href="#callout_lambdas_and_the_ranges_library_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="p">(</code><code class="n">price</code><code class="w"> </code><code class="o">-</code><code class="w"> </code><code class="n">first</code><code class="p">)</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="n">required_profit</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO14-1" href="#co_lambdas_and_the_ranges_library_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Captures <code>first</code> by reference and <code>required_profit</code> by value</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO14-2" href="#co_lambdas_and_the_ranges_library_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Changes <code>first</code> (a silly idea here, too!)</p></dd>
</dl>

<p>Since <code>first</code> is <code>const</code>, you will get a compiler error along the lines of:</p>

<pre data-type="programlisting" data-code-language="bash">assignment<code class="w"> </code>of<code class="w"> </code>read-only<code class="w"> </code>reference<code class="w"> </code><code class="s1">'first'</code><code class="w"></code></pre>

<p>This is just another reason why it’s sensible to mark variables <code>const</code> when you don’t intend to change them.</p>

<p>If you change the declaration to be non-<code>const</code>, your new code will compile:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">double</code><code class="w"> </code><code class="n">first</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="p">.</code><code class="n">front</code><code class="p">();</code><code class="w"></code></pre>

<p>You’ve learned how to use <code>[=]</code> to capture anything needed by value, and you’ve seen a mixture of captures by reference and by value.
If you want to capture <em>everything</em> you need by reference, use <code>[&amp;]</code>.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>If you capture variables by reference and the lambda outlives the referenced variable, you can get into trouble.
This is called a <em>dangling reference</em>.
If you capture a variable by reference, it might go out of scope.
Here’s an example from an <a href="https://www.youtube.com/watch?v=NAuFNXIsbtw">ACCU talk: </a> “Let’s Look at Lambdas,” by Roger Orr:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;functional&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">function</code><code class="o">&lt;</code><code class="kt">int</code><code class="p">(</code><code class="kt">int</code><code class="p">)</code><code class="o">&gt;</code><code class="w"> </code><code class="n">make_adder</code><code class="p">(</code><code class="kt">int</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-1" href="#callout_lambdas_and_the_ranges_library_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="p">[</code><code class="o">&amp;</code><code class="n">value</code><code class="p">]</code><code class="p">(</code><code class="kt">int</code><code class="w"> </code><code class="n">n</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">n</code><code class="w"> </code><code class="o">+</code><code class="w"> </code><code class="n">value</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-2" href="#callout_lambdas_and_the_ranges_library_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-3" href="#callout_lambdas_and_the_ranges_library_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">add_ten</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">make_adder</code><code class="p">(</code><code class="mi">10</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-4" href="#callout_lambdas_and_the_ranges_library_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">int</code><code class="w"> </code><code class="n">result</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">add_ten</code><code class="p">(</code><code class="mi">9</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-5" href="#callout_lambdas_and_the_ranges_library_CO15-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">result</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO15-6" href="#callout_lambdas_and_the_ranges_library_CO15-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-1" href="#co_lambdas_and_the_ranges_library_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p><code>value</code> in scope</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-2" href="#co_lambdas_and_the_ranges_library_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Uses <code>value</code> by reference</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-3" href="#co_lambdas_and_the_ranges_library_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p><code>value</code> goes out of scope</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-4" href="#co_lambdas_and_the_ranges_library_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Gets a <code>std::function</code> using <code>value</code> by reference, which has gone out of scope</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-5" href="#co_lambdas_and_the_ranges_library_CO15-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Calls the <code>std::function</code></p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO15-6" href="#co_lambdas_and_the_ranges_library_CO15-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Outputs something, but <code>result</code> provokes undefined behavior</p></dd>
</dl>

<p>Using clang on <a href="https://godbolt.org/z/nW7vqTPcT">Godbolt</a> outputs 18, rather than 19.</p>
</div>

<p>Put the capture group back to <code>[first, required_profit]</code>, then make <code>first</code> <code>const</code> again, as you had it before—​while changing <code>first</code> demonstrates what’s possible, it’s not neccesarily sensible for this project.</p>

<p>Some best practices for lambdas and capturing are</p>

<ul>
<li>
<p>Capture by value, unless it is too expensive</p>
</li>
<li>
<p>Capture explicitly, and try to avoid <code>[=]</code> and <code>[&amp;]</code></p>
</li>
<li>
<p>Try to avoid mutable lambdas</p>
</li>
</ul>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Composing views"><div class="sect2" id="id113">
<h2>Composing views</h2>

<p>It’s easy to compose views together.
Create a new file called <em>views_experiment.cpp</em> and let’s find out how.  You’ll hard-code a vector of values this time, to save you from typing in more numbers.
Again, you will filter out negative prices, then show prices that are cheaper than a required price.</p>

<p>Up to now, you’ve copied views into a vector for analysis.
Now you’ll use the view directly.</p>

<p>You can use another <code>view</code> called <code>take_while</code> to take prices while they are lower than the required price.
<code>take_while</code>, like <code>filter</code>, is a range adaptor.
<em>Range adaptors</em> provide a view of the underlying data, filtering it by the criteria you specify or transforming it by a function.
There are many other range adaptors, including <code>take</code> (which takes a specific number of elements), <code>skip</code> (which ignores a number of elements), and <code>skip_while</code> (which ignores elements that match a predicate).
<a href="https://en.cppreference.com/w/cpp/header/ranges">CppReference</a> gives a full list, if you want to experiment further.
<a data-type="xref" href="#fig_6_2">Figure 6-2</a> shows the view that results if your <code>take_while</code> elements are squares.</p>

<figure><div id="fig_6_2" class="figure">
<img src="assets/fig_6_2.png" alt="A view taking elements while they are squares" width="1280" height="720"/>
<h6><span class="label">Figure 6-2. </span>A view taking elements while they are squares</h6>
</div></figure>

<p>You can create a view that takes initial prices lower than the required value, and then stream the first view to demonstrate that the <code>take_while</code> view has no effect on the first:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;ranges&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="w"> </code><code class="n">prices</code><code class="p">{</code><code class="mf">3.76</code><code class="p">,</code><code class="w"> </code><code class="mf">1.5</code><code class="p">,</code><code class="w"> </code><code class="mf">-1.0</code><code class="p">,</code><code class="w"> </code><code class="mf">3.0</code><code class="p">,</code><code class="w"> </code><code class="mf">4.0</code><code class="p">,</code><code class="w"> </code><code class="mf">-2.0</code><code class="p">,</code><code class="w"> </code><code class="mf">99.4</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-1" href="#callout_lambdas_and_the_ranges_library_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">required_price</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">4.75</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-2" href="#callout_lambdas_and_the_ranges_library_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">non_negative</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-3" href="#callout_lambdas_and_the_ranges_library_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">too_cheap</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="n">required_price</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">x</code><code class="p">)</code><code class="w"> </code><code class="w">
</code><code class="w">                     </code><code class="p">{</code><code class="w">
</code><code class="w">                         </code><code class="k">return</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">&lt;</code><code class="o">=</code><code class="w"> </code><code class="n">required_price</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">                     </code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-4" href="#callout_lambdas_and_the_ranges_library_CO16-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">filter</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w">  </code><code class="n">non_negative</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-5" href="#callout_lambdas_and_the_ranges_library_CO16-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">take_while</code><code class="p">(</code><code class="n">valid_prices</code><code class="p">,</code><code class="w"> </code><code class="n">too_cheap</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-6" href="#callout_lambdas_and_the_ranges_library_CO16-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Too cheap:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-7" href="#callout_lambdas_and_the_ranges_library_CO16-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">no_good</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-8" href="#callout_lambdas_and_the_ranges_library_CO16-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">All prices:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-9" href="#callout_lambdas_and_the_ranges_library_CO16-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">valid_prices</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO16-10" href="#callout_lambdas_and_the_ranges_library_CO16-8"><img src="assets/8.png" alt="8" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-1" href="#co_lambdas_and_the_ranges_library_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Creates a vector of prices</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-2" href="#co_lambdas_and_the_ranges_library_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Defines a required price</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-3" href="#co_lambdas_and_the_ranges_library_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Creates a lambda to filter out negatives</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-4" href="#co_lambdas_and_the_ranges_library_CO16-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Creates a lambda to take prices while they are less than the required price</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-5" href="#co_lambdas_and_the_ranges_library_CO16-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Creates a view filtering out negatives</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-6" href="#co_lambdas_and_the_ranges_library_CO16-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Takes a view of prices lower than required</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-7" href="#co_lambdas_and_the_ranges_library_CO16-7"><img src="assets/7.png" alt="7" width="12" height="12"/></a></dt>
<dd><p>Displays the prices that are too cheap</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO16-8" href="#co_lambdas_and_the_ranges_library_CO16-9"><img src="assets/8.png" alt="8" width="12" height="12"/></a></dt>
<dd><p>Shows the first view is unaltered</p></dd>
</dl>

<p>Build this single main file. You aren’t using any other source files this time, so you only need to use <em>main.cpp</em> in the instructions.
The output shows the prices that are too cheap, then shows that your first view is unchanged:</p>

<pre data-type="programlisting" data-code-language="bash">Too<code class="w"> </code>cheap:<code class="w"></code>
<code class="m">3</code>.76<code class="w"></code>
<code class="m">1</code>.5<code class="w"></code>
<code class="m">3</code><code class="w"></code>
<code class="m">4</code><code class="w"></code>
All<code class="w"> </code>prices:<code class="w"></code>
<code class="m">3</code>.76<code class="w"></code>
<code class="m">1</code>.5<code class="w"></code>
<code class="m">3</code><code class="w"></code>
<code class="m">4</code><code class="w"></code>
<code class="m">99</code>.4<code class="w"></code></pre>

<p>You can actually compose the two views together in one line of code.
There are two ways to create a view.
So far, your parameters have been the container or view, and a predicate:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">prices</code><code class="p">,</code><code class="w"> </code><code class="n">non_negative</code><code class="p">);</code><code class="w"></code></pre>

<p>An alternative starts an expression with the <code>prices</code> and uses the pipe operator <code>|</code> to send this expression to a view with a predicate:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">);</code><code class="w"></code></pre>

<p>Now, <code>valid_prices</code> is another <code>view</code>, so you could use another pipe to obtain the second view:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">);</code><code class="w"></code>
<code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">valid_prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">);</code><code class="w"></code></pre>

<p>You might start running out of ideas for clear names at this rate.
You can chain or compose these two views together in one statement:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO17-1" href="#callout_lambdas_and_the_ranges_library_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">                      </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_lambdas_and_the_ranges_library_CO17-2" href="#callout_lambdas_and_the_ranges_library_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO17-1" href="#co_lambdas_and_the_ranges_library_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Filters negative prices</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO17-2" href="#co_lambdas_and_the_ranges_library_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Takes prices while they are too cheap</p></dd>
</dl>

<p>This might not seem all that useful for a couple of views, but chaining <em>lots</em> of views together can make your code clearer.</p>

<p>Swap your view experiment to use the pipe:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;ranges&gt;</code><code class="cp"></code>
<code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">vector</code><code class="w"> </code><code class="n">prices</code><code class="p">{</code><code class="mf">3.76</code><code class="p">,</code><code class="w"> </code><code class="mf">1.5</code><code class="p">,</code><code class="w"> </code><code class="mf">-1.0</code><code class="p">,</code><code class="w"> </code><code class="mf">3.0</code><code class="p">,</code><code class="w"> </code><code class="mf">4.0</code><code class="p">,</code><code class="w"> </code><code class="mf">-2.0</code><code class="p">,</code><code class="w"> </code><code class="mf">99.4</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">required_price</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">4.75</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">non_negative</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[](</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">too_cheap</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="n">required_price</code><code class="p">](</code><code class="kt">double</code><code class="w"> </code><code class="n">x</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">&lt;=</code><code class="w"> </code><code class="n">required_price</code><code class="p">;</code><code class="w"> </code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">)</code><code class="w"></code>
<code class="w">                          </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Too cheap:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code>
<code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">no_good</code><code class="p">)</code><code class="w"> </code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>When you build and run your program, you’ll see which prices are too cheap:</p>

<pre data-type="programlisting" data-code-language="bash">Too<code class="w"> </code>cheap:<code class="w"></code>
<code class="m">3</code>.76<code class="w"></code>
<code class="m">1</code>.5<code class="w"></code>
<code class="m">3</code><code class="w"></code>
<code class="m">4</code><code class="w"></code></pre>

<p>As before, the prices were 3.76, 1.5, -1.0, 3.0, 4.0, -2.0, 99.4, and you required 4.75.
Once the negative prices are filtered, all but the last value, 99.4, are too cheap.</p>

<p>There’s one last important detail about views that might not be immediately obvious.
Let’s have a look.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Lazy views"><div class="sect2" id="id114">
<h2>Lazy views</h2>

<p>When you copy data into a vector, <em>all</em> the data gets copied, regardless of whether you want to view it.
In contrast, a view doesn’t do anything until you use it.
This is called <em>lazy evaluation</em>, because the view is evaluated only when asked.</p>

<p>When you created a view, you chained  a <code>filter</code> and <code>take_while</code> together:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">)</code><code class="w"></code>
<code class="w">                      </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">);</code><code class="w"></code></pre>

<p>However, the filtering and taking don’t happen yet.
You can prove this by adding a <code>std::cout</code> call in the second predicate:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;ranges&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;vector&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">vector</code><code class="w"> </code><code class="n">prices</code><code class="p">{</code><code class="mf">3.76</code><code class="p">,</code><code class="w"> </code><code class="mf">1.5</code><code class="p">,</code><code class="w"> </code><code class="mf">-1.0</code><code class="p">,</code><code class="w"> </code><code class="mf">3.0</code><code class="p">,</code><code class="w"> </code><code class="mf">4.0</code><code class="p">,</code><code class="w"> </code><code class="mf">-2.0</code><code class="p">,</code><code class="w"> </code><code class="mf">99.4</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">required_price</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mf">4.75</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">non_negative</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">price</code><code class="p">)</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="k">return</code><code class="w"> </code><code class="n">price</code><code class="w"> </code><code class="o">&gt;</code><code class="o">=</code><code class="w"> </code><code class="mf">0.0</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">too_cheap</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="p">[</code><code class="n">required_price</code><code class="p">]</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">x</code><code class="p">)</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Comparing </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO18-1" href="#callout_lambdas_and_the_ranges_library_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="n">x</code><code class="w"> </code><code class="o">&lt;</code><code class="o">=</code><code class="w"> </code><code class="n">required_price</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">auto</code><code class="w"> </code><code class="n">no_good</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">)</code><code class="w">
</code><code class="w">                          </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">views</code><code class="o">:</code><code class="o">:</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO18-2" href="#callout_lambdas_and_the_ranges_library_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Too cheap:</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_lambdas_and_the_ranges_library_CO18-3" href="#callout_lambdas_and_the_ranges_library_CO18-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">for</code><code class="p">(</code><code class="kt">double</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">:</code><code class="w"> </code><code class="n">no_good</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO18-4" href="#callout_lambdas_and_the_ranges_library_CO18-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">p</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO18-1" href="#co_lambdas_and_the_ranges_library_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Adds output when called</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO18-2" href="#co_lambdas_and_the_ranges_library_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Creates a view, but doesn’t call the predicates yet</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO18-3" href="#co_lambdas_and_the_ranges_library_CO18-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Displays a message, as before, after the view is created</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO18-4" href="#co_lambdas_and_the_ranges_library_CO18-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Iterates the view, so it calls the predicates now</p></dd>
</dl>

<p>When you build and run your code this time, you will see the predicate’s message after the “Too cheap:” output:</p>

<pre data-type="programlisting" data-code-language="bash"><code>Too</code><code class="w"> </code><code>cheap:</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO19-1" href="#callout_lambdas_and_the_ranges_library_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code>Comparing</code><code class="w"> </code><code class="m">3</code><code>.76</code><code class="w"> </code><a class="co" id="co_lambdas_and_the_ranges_library_CO19-2" href="#callout_lambdas_and_the_ranges_library_CO19-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="m">3</code><code>.76</code><code class="w">
</code><code>Comparing</code><code class="w"> </code><code class="m">1</code><code>.5</code><code class="w">
</code><code class="m">1</code><code>.5</code><code class="w">
</code><code>Comparing</code><code class="w"> </code><code class="m">3</code><code class="w">
</code><code class="m">3</code><code class="w">
</code><code>Comparing</code><code class="w"> </code><code class="m">4</code><code class="w">
</code><code class="m">4</code><code class="w">
</code><code>Comparing</code><code class="w"> </code><code class="m">99</code><code>.4</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO19-1" href="#co_lambdas_and_the_ranges_library_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Displays a message after the view is made</p></dd>
<dt><a class="co" id="callout_lambdas_and_the_ranges_library_CO19-2" href="#co_lambdas_and_the_ranges_library_CO19-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Shows that the predicate is called when the view is used</p></dd>
</dl>

<p>Lazy evalution can make code more efficient.</p>
<div data-type="tip"><h6>Tip</h6>
<p>Don’t be shy about adding output to your code to see what’s happening when. A well-known computer scientist, Brian Kernighan, once said,
“The most effective debugging tool is still careful thought, coupled with judiciously placed print statements.”</p>
</div>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id115">
<h1>Conclusion</h1>

<p>You used lambdas and views from the ranges library in this chapter.
Views let you pick certain elements from a range.
Many use predicates, which can be named functions, like your <code>negative</code> function, or anonymous functions, known as lambdas.
Lambdas look like named functions, but don’t state the return type, and they start with <code>[]</code> (known as the <em>capture</em>).
You used your lambda to prompt for numbers:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[]</code><code class="w"> </code><code class="p">()</code><code class="w"> </code><code class="p">{</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"> </code><code class="p">}</code><code class="w"></code></pre>

<p>You learned how to capture variables for a lambda, by reference and by value—​two important ideas you have met before and will use again and again.</p>

<ul>
<li>
<p>Using <code>[=]</code> captures everything you need by value.</p>
</li>
<li>
<p>You cannot change by-value captures, but you can add <code>mutable</code> if you want to.</p>
</li>
<li>
<p>Using <code>[&amp;]</code> captures everything you need by reference, which can be clearer than <code>mutable</code>.</p>
</li>
<li>
<p>You can name specific variables to capture, like <code>[first]</code>.</p>
</li>
<li>
<p>You can mix by-reference and by-value captures, like <code>[&amp;first, required_profit]</code>.</p>
</li>
</ul>

<p>You used <code>std::function</code> to take a prompt, so that you could use a <code>no_op</code> lambda in tests:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[](){}</code><code class="w"></code></pre>

<p>The prompt function you used was <code>&lt;void()&gt;</code>, so it takes no parameters and has a <code>void</code> return.
You can use named functions as well as lambdas in a <code>std::function</code>, provided the signature matches.</p>

<p>You also used lambdas in ranges’ views.
Views are lazy, meaning they are evaluated on demand.
You can compose views using the pipe operator, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">auto</code><code class="w"> </code><code class="n">data</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">prices</code><code class="w"> </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">filter</code><code class="p">(</code><code class="n">non_negative</code><code class="p">)</code><code class="w"></code>
<code class="w">                   </code><code class="o">|</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">views</code><code class="o">::</code><code class="n">take_while</code><code class="p">(</code><code class="n">too_cheap</code><code class="p">);</code><code class="w"></code></pre>

<p>You also saw how to convert a view to a vector, using either <code>std::ranges::to</code> or  the view’s <code>begin</code> and <code>end</code>.
This gives you a copy of the elements in the view.
You did this to call functions in <em>analysis.cpp</em>, and later in this book, you will see how to write functions that take a view directly.</p>

<p>You built a larger program, reusing your existing source files.
You now have a simple trading strategy.
It’s not going to make you rich, but you know so much more C++ now.</p>

<p>I suspect you might be bored with typing in made-up prices by now.
In the next chapter, you’ll find out how to make your computer generate those fictitious prices for you, using random numbers.</p>
</div></section>
</div></section></div></div></body></html>