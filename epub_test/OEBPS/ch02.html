<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xml:lang="en" lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>Introducing C++</title><link rel="stylesheet" type="text/css" href="override_v1.css"/><link rel="stylesheet" type="text/css" href="epub.css"/></head><body><div id="book-content"><div id="sbo-rt-content"><section data-type="chapter" epub:type="chapter" data-pdf-bookmark="Chapter 2. Variables and Keyboard Input"><div class="chapter" id="chapter_two">
<h1><span class="label">Chapter 2. </span>Variables and Keyboard Input</h1>

<aside data-type="sidebar" epub:type="sidebar"><div class="sidebar" id="id118">
<h1>A Note for Early Release Readers</h1>
<p>With Early Release ebooks, you get books in their earliest form—the author’s raw and unedited content as they write—so you can take advantage of these technologies long before the official release of these titles.</p>

<p>This will be the 2nd chapter of the final book.</p>

<p>If you have comments about how we might improve the content and/or examples in this book, or if you notice missing material within this chapter, please reach out to the editor at <em><EMAIL></em>.</p>
</div></aside>

<p>In this chapter, you will accept input in another short program.
You will learn about declaring variables and practice writing more functions.
You’ll also start to think about general approaches to handling errors, which I’ll go into more in the next chapter.
With three chapters under your belt, you’ll be ready to start building a larger program in the following chapter. The larger program will input and analyze stock-price data, and you will add to this project over the rest of the book.</p>

<p>First, you need to be able to take input, so let’s find out how. You will write a program in a single file again. If you need a reminder on how to build your code, look back at <a href="ch01.html#chapter_1_build_instructions">the first chapter’s instructions</a>.</p>

<p>When you wrote output, you used two approaches: <code>std::println</code> and <code>std::cout</code>’s operator <code>&lt;&lt;</code>. You have one option for input in C++ using an input stream <code>std::cin</code>.
Input needs to go somewhere, so you must start with a place for it.</p>






<section data-type="sect1" data-pdf-bookmark="Declaring variables"><div class="sect1" id="chap_2_variables">
<h1>Declaring variables</h1>

<p>Create a new source file and call it <em>input.cpp</em>. This will give you a place to experiment.
You will write a program to get input shortly, but first, you need a variable to take that input.
All variables in C++ have a <em>type</em>. You met <code>int</code> in the last chapter as the return from <code>main</code> in <a data-type="xref" href="ch01.html#first_main_code_block">Example 1-1</a>.
You can declare a variable by giving a type and a name:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"></code></pre>

<p>Since you haven’t assigned <code>number</code> a value, it could be anything.
It isn’t safe to print this out. Reading an <em>uninitialized</em> variable was <em>undefined behavior</em> until C++26, which means that anything, including bad things, can happen.
C++26 made reading an uninitialized variable <em>erroneous behavior</em>, which means you are likely to get a warning or error.
Assigning a value to the variable is extra work, and it’s up to you to do this if you need to.
C++ can be very efficient, but this efficiency can mean potential problems if you aren’t careful.
For example, in a bigger program, you cannot predict any results.</p>

<p>To avoid trouble, get in the habit of always initializing your variables.
You can explicitly state a value you want. For example:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="o">=</code><code class="mi">0</code><code class="p">;</code><code class="w"></code></pre>

<p>However, a more general approach uses <em>brace initialization</em>, which means using <code>{}</code> after the variable name:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">{};</code><code class="w"></code></pre>

<p>The <code>number</code> is still initialized with a zero.
This modern approach was introduced in C++11, so don’t forget to use the <code>std</code> flag when you build code using this.
You can put a number in the braces, like <code>{0}</code>, but you don’t need to if you want a zero.
The ISO <a href="https://isocpp.github.io/CppCoreGuidelines/CppCoreGuidelines#es23-prefer-the%E2%80%94%E2%80%8Binitializer-syntax">Core Guidelines</a> gives more details on this {}-initializer syntax, and you will see some other advantages later.</p>
<div data-type="tip"><h6>Tip</h6>
<p>The ISO <a href="https://isocpp.github.io/CppCoreGuidelines/CppCoreGuidelines">Core Guidelines</a> are an open source collection of guidelines edited by Bjarne Stroustrup, the inventor of C++ and Herb Sutter, a prominent C++ expert. The guidelines aim to help C++ programmers to write simpler, more efficient, more maintainable code.</p>
</div>

<p>Since <code>number</code> is a <em>variable</em>, you can vary its value, for example by changing it later:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">42</code><code class="p">;</code><code class="w"></code></pre>

<p>C++ allows you to flag a variable as <code>const</code>, short for <em>constant</em>, meaning you do not intend to change it from the initial value.
If you try to, you’ll get an error. The following does not compile:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="mi">1</code><code class="p">};</code><code class="w"></code>
<code class="n">number</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="mi">73</code><code class="p">;</code><code class="w"></code></pre>

<p>You will see more uses of <code>const</code> over the course of this book, but for our purposes here, you want to change <code>number</code> based on input, so you won’t use <code>const</code>.</p>

<p>In your <em>input.cpp</em> file, declare an <code>int</code> inside the <code>main</code> function.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO1-1" href="#callout_variables_and_keyboard_input_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO1-1" href="#co_variables_and_keyboard_input_CO1-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Declares a number and use {} to initialize it</p></dd>
</dl>

<p>After saving your file, check that this builds OK, using the <code>Wall</code> and <code>std</code> flags. If so, you’re ready to get some input.
The warning flag <code>Wall</code> will cause a compiler <em>warning</em> pointing out that you have an <em>unused variable</em>.
That’s a good thing–remember, a warning is not an error.
However, warnings often point out something you need to think about, so watch for them.
You will use the variable shortly, so the warning will stop.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Character input"><div class="sect1" id="id54">
<h1>Character input</h1>

<p>You obtain input from <code>std::cin</code> (pronounced “see in”), which lives in the <code>&lt;iostream&gt;</code> header along with <code>std::cout</code>.
Output uses the operator <code>&lt;&lt;</code>. Input goes the other way, so it uses the operator <code>&gt;&gt;</code>, called the <em>stream extraction operator</em>.
Any leading whitespace, such as a space or tab, will be ignored. You’ll signal the end of your input by pressing Enter (or Return).
You can type any characters until Enter, but whitespace will break the input into separate parts, as indicated in <a data-type="xref" href="#fig_2_1">Figure 2-1</a>.</p>

<figure><div id="fig_2_1" class="figure">
<img src="assets/fig_2_1.png" alt="A character input stream" width="1280" height="720"/>
<h6><span class="label">Figure 2-1. </span>Input is a stream of characters, split into parts by whitespace.</h6>
</div></figure>

<p>When you extract input, you store it in a variable, which is why you started by declaring a <code>number</code> in the last section. You can try to extract other types too, but let’s start with an <em>int</em>.</p>

<p>Add code to your main function in <em>input.cpp</em>, as shown in <a data-type="xref" href="#input_main_code_block">Example 2-1</a>.
You need a variable to store the <code>number</code>, code to get the input, and code showing what the input was.
Adding a helpful message asking for numeric input first tells anyone using the program what to do. Using a <code>&gt;</code> symbol makes it clear the program is prompting for input.</p>
<div id="input_main_code_block" data-type="example">
<h5><span class="label">Example 2-1. </span>Attempt to input whole numbers (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="c1"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-1" href="#callout_variables_and_keyboard_input_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-2" href="#callout_variables_and_keyboard_input_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-3" href="#callout_variables_and_keyboard_input_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-4" href="#callout_variables_and_keyboard_input_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-5" href="#callout_variables_and_keyboard_input_CO2-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO2-6" href="#callout_variables_and_keyboard_input_CO2-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-1" href="#co_variables_and_keyboard_input_CO2-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes the input/output stream header</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-2" href="#co_variables_and_keyboard_input_CO2-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Starts the <code>main</code> function</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-3" href="#co_variables_and_keyboard_input_CO2-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Shows a helpful message, ending with a newline, then <code>&gt;</code> to prompt for input</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-4" href="#co_variables_and_keyboard_input_CO2-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Declares a variable and initializes it</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-5" href="#co_variables_and_keyboard_input_CO2-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Tries to stream in a number</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO2-6" href="#co_variables_and_keyboard_input_CO2-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Streams out the number</p></dd>
</dl></div>

<p>You saw <code>std::println</code> and <code>std::cout</code> in the last chapter.
I will stick with <code>std::cout</code> in this chapter, because it shows the parallels between the two stream operators.
You use <code>&lt;&lt;</code> to stream things out, and <code>&gt;&gt;</code> to stream things in.</p>

<p>Build your code and try it out.
At this point, you probably don’t need me to remind you to save your file and use the appropriate flags.</p>

<p>You will see the message and the prompt to enter a number:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number.<code class="w"></code>
&gt;<code class="w"></code></pre>

<p>Nothing further will happen until you type something and press Enter.</p>

<p>Try some positive and negative numbers. Get inventive: try fractions or pretend your cat walked over your keyboard.
(If you have a cat, you know this can happen.)
You can change the type from an <code>int</code> to a more general numeric type to deal with numbers that have a decimal part.</p>

<p>When you deal with input, things can go wrong, so the next section introduces some considerations.</p>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Detecting input problems"><div class="sect1" id="id55">
<h1>Detecting input problems</h1>

<p>Your program is trying to obtain a number from the keyboard.
The stream of characters the user types might not be a number, though.
What happens if you don’t type in a whole number, or even a number at all?</p>








<section data-type="sect2" data-pdf-bookmark="Input of real numbers"><div class="sect2" id="id56">
<h2>Input of real numbers</h2>

<p>Let’s consider numbers with decimal parts first.
When you used <code>std::cin &gt;&gt; number;</code> you asked for the <code>int</code> overload, or version, of operator <code>&gt;&gt;</code>. If you tried a real number, like -1.4, rather than a whole number, the output only picked up the whole-number part: -1.
The <code>.4</code> will be left in the stream. You can find it later or ask the stream to ignore these characters, which
I will show you how to do shortly.</p>

<p>A simpler approach is using another numeric type, a <code>double</code>, which is suitable for <em>floating point</em> values. (This type’s full name is <em>double- precision floating point</em>.)
As a reminder, integers are whole numbers, like -1, while doubles can have digits after the decimal point, so they include integers as well as numbers like -1.4.</p>

<p>Try changing the type of <code>number</code> in your main function and see what happens:</p>
<div id="float_main_code_block" data-type="example">
<h5><span class="label">Example 2-2. </span>Input of doubles (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO3-1" href="#callout_variables_and_keyboard_input_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO3-1" href="#co_variables_and_keyboard_input_CO3-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Declares a <code>double</code> instead of an <code>int</code></p></dd>
</dl></div>

<p>As before, you’ll be prompted to enter a number.</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number.<code class="w"></code>
&gt;<code class="w"></code></pre>

<p>Now you can enter numbers with a decimal part.
Both the <code>double</code> and the <code>int</code> are <a href="https://en.cppreference.com/w/cpp/language/types">fundamental types</a>, and the exact range of values they support can vary between toolchains.
You can find out the largest available value by using a function called <code>max</code> in the <code>&lt;limits&gt;</code> header.
The <code>max</code> function comes from <code>std::numeric_limits</code>, which is a general type, or from <em>template</em>.
You provide the template’s type in angle brackets, then call the <code>max</code> function using the scope resolution operator <code>::</code>, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="n">largest_int</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="kt">int</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO4-1" href="#callout_variables_and_keyboard_input_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="kt">double</code><code class="w"> </code><code class="n">largest_double</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO4-2" href="#callout_variables_and_keyboard_input_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO4-1" href="#co_variables_and_keyboard_input_CO4-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Asks for <code>int</code>’s maximum</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO4-2" href="#co_variables_and_keyboard_input_CO4-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Asks for <code>double</code>’s maximum</p></dd>
</dl>

<p>The function lives in a <em>class template</em>.
You’ve already learned that classes, like <code>std::cin</code>, group functions together.
Templates are like cookie cutters or patterns for code of various types.
They’re like Java or C#’s generics, but much more powerful.
So, a class template is a pattern for making classes.
You have used the scope resolution operator to use types and objects from the standard library like this: <code>std::</code>.
You can also call functions declared in a type, called <em>static member functions</em> using <code>::</code>.
You will learn more about this later.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Detecting problems with numbers"><div class="sect2" id="id57">
<h2>Detecting problems with numbers</h2>

<p>Now, since both <code>int</code> and <code>double</code> have a minimum and maximum value, if you (or your cat), press a digit for several seconds, you can end up with a number that’s too big for its type.
The extraction operator will use as many characters as fit in the type, leaving unused characters in the stream’s buffer.
I will show you how to check if characters are left over, and you will learn some more C++ syntax on the way. Then, in the next section, I’ll show you a simpler way to deal with invalid input.
Try the code here in a file called input_experiment.cpp.</p>

<p>Input streams, including <code>std::cin</code>, have an <code>eof</code> function, which stands for for <em>end of file</em>, which tells you if there are more characters left in the stream after an attempt to read.
(Files are another type of stream you will meet later.)
You can check for <code>std::cin</code>’s <code>eof</code> after the read, using the dot operator:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">.</code><code class="n">eof</code><code class="p">())</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Unused input</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p><code>eof</code> is a member function of the input stream, so it is part of <code>std::cin</code> itself.
Member functions need to be called via the dot (period) operator.
You used <code>::</code> to call <code>numeric_limits</code>’s static <code>max</code> function earlier.
Now you are calling an <em>instance</em> function, so use a dot.
The <code>!</code> symbol means <code>not</code>, so this checks whether you have reached the end of the stream.
When there are further characters waiting in the stream after the <code>number</code> has been read, <code>std::cin</code> is <em>not</em> at <code>eof</code>.
If you type in a smaller number and press Enter, the <code>\n</code> character will still be in the stream, so valid input will also not be at the <code>eof</code>. You can take a peek at the next character, using <code>std::cin</code>’s <code>peek</code> function, and compare it with a new line:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">.</code><code class="n">peek</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">!</code><code class="o">=</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO5-1" href="#callout_variables_and_keyboard_input_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO5-1" href="#co_variables_and_keyboard_input_CO5-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Compares the next character with a new line using <code>!=</code> for inequality</p></dd>
</dl>

<p>So you need to check two things. You can use the symbol <code>&amp;&amp;</code> to that check something <em>and</em> something else are both true:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">.</code><code class="n">eof</code><code class="p">()</code><code class="w"> </code><code class="o">&amp;&amp;</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">.</code><code class="n">peek</code><code class="p">()</code><code class="o">!=</code><code class="sc">'\n'</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>Add this to check the <code>main</code> function in your <em>input_experiment.cpp</em> file, and try some values to test it out.
Add the maximum possible value to the prompt for input, so you know what your toolchain supports:</p>
<div id="try_to_spot_input_problems" data-type="example">
<h5><span class="label">Example 2-3. </span>An attempt to spot input problems (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">largest</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="kt">double</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO6-1" href="#callout_variables_and_keyboard_input_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number up to </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">largest</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO6-2" href="#callout_variables_and_keyboard_input_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">.</code><code class="n">eof</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><code class="o">&amp;</code><code class="o">&amp;</code><code class="w"> </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">.</code><code class="n">peek</code><code class="p">(</code><code class="p">)</code><code class="o">!</code><code class="o">=</code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO6-3" href="#callout_variables_and_keyboard_input_CO6-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Unused input</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO6-4" href="#callout_variables_and_keyboard_input_CO6-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO6-1" href="#co_variables_and_keyboard_input_CO6-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Finds the maximum <code>double</code></p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO6-2" href="#co_variables_and_keyboard_input_CO6-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Prompts for input</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO6-3" href="#co_variables_and_keyboard_input_CO6-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Checks if there is input left over which isn’t a newline character</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO6-4" href="#co_variables_and_keyboard_input_CO6-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Reports that something bad happened</p></dd>
</dl></div>

<p>When you build and run this, you will see the maximum in <a href="https://en.wikipedia.org/wiki/Scientific_notation">scientific notation</a>, like this:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number<code class="w"> </code>up<code class="w"> </code>to<code class="w"> </code><code class="m">1</code>.79769e+308.<code class="w"></code>
&gt;<code class="w"></code></pre>

<p>Typing a single number smaller than the maximum is fine. If you type a few numbers with spaces, the program picks the first, then reports that there is leftover input:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number<code class="w"> </code>up<code class="w"> </code>to<code class="w"> </code><code class="m">1</code>.79769e+308.<code class="w"></code>
&gt;4<code class="w"> </code><code class="m">5</code><code class="w"> </code><code class="m">6</code><code class="w"></code>
<code class="m">4</code><code class="w"></code>
Unused<code class="w"> </code>input<code class="w"></code></pre>

<p>Looking good so far.
If you type a very large number, like 1e+309, you should see the message <code>Unused input</code>, but the output will show a number, like this:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number<code class="w"> </code>up<code class="w"> </code>to<code class="w"> </code><code class="m">1</code>.79769e+308.<code class="w"></code>
&gt;1e+309<code class="w"></code>
<code class="m">1</code>.79769e+308<code class="w"></code>
Unused<code class="w"> </code>input<code class="w"></code></pre>

<p>That number is wrong!
To be fair, it is the largest possible <code>double</code>.
So let’s find some better ways to handle input problems.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Detecting more general problems"><div class="sect2" id="id58">
<h2>Detecting more general problems</h2>

<p>If you type a digit or two and then some letters or other characters, the program will take as much from the stream as possible for the <code>number</code>.
If I type a digit and a letter, I get this:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number<code class="w"> </code>up<code class="w"> </code>to<code class="w"> </code><code class="m">1</code>.79769e+308.<code class="w"></code>
&gt;1a<code class="w"></code>
<code class="m">1</code><code class="w"></code>
Unused<code class="w"> </code>input<code class="w"></code></pre>

<p>What happens if you just type a letter, then press Enter? Think it through before you try.</p>

<p>Now, <code>double number{};</code> sets the variable to zero. The extraction <code>std::cin &gt;&gt; number;</code> will stop at the Enter, so <code>number</code> will remain at zero, and the letter will be left over in the input.
If you run this, you therefore get:</p>

<pre data-type="programlisting" data-code-language="bash">Please<code class="w"> </code>enter<code class="w"> </code>a<code class="w"> </code>number<code class="w"> </code>up<code class="w"> </code>to<code class="w"> </code><code class="m">1</code>.79769e+308.<code class="w"></code>
&gt;q<code class="w"></code>
<code class="m">0</code><code class="w"></code>
Unused<code class="w"> </code>input<code class="w"></code></pre>

<p>This situation isn’t the same as having too <em>many</em> digits, but you can solve these problems in the same way.
This program expected numeric input but got something else.
Along with the <code>eof</code> function, a stream has a <code>fail</code> function, which tells you if its extraction has gone wrong.</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">.</code><code class="n">fail</code><code class="p">())</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Something went wrong!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>In general, lots of other things could go wrong.
For completeness, the stream also has a <code>bad</code> function that indicates if any other bad things have happened.
<a href="https://en.cppreference.com/w/cpp/io/ios_base/iostate">CppReference</a> gives a long list of possible problems. Fortunately, you don’t need to know about all of them.
Rather than checking <code>fail</code> and <code>bad</code> directly, C++ gives us a neater way to check that you’re good, like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="sc">'\n'</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code>
<code class="k">else</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;&lt;</code><code class="w"> </code><code class="s">"Bother!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>The <code>if(stream)</code> is shorthand for <code>if(stream.operator bool())</code>, which is a bit of a mouthful.
Such shorthands are called <em>syntactic sugar</em>.
An <code>if</code> statement checks a conditional expression, so the expression needs to give a <code>bool</code>.
<code>std::cin</code> has an operator returning a <code>bool</code>, so the stream can be <em>converted</em> to a <code>bool</code>, allowing you to use it in a <em>Boolean context</em>, for example, inside an <code>if</code> statement.
The operator <code>bool</code> checks if the stream is good, so a <code>fail</code> or <code>bad</code> state gives <code>false</code>.
In effect, the syntactic sugar saves you the work of explicitly checking the state.</p>

<p>The operator <code>bool</code> is provided for use in a Boolean context only.
You can’t assign the stream to a <code>bool</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">std</code><code class="o">::</code><code class="n">cin</code><code class="p">;</code><code class="w"></code></pre>

<p>If you try this line of code, you will get a compiler error.
A <a href="https://compiler-explorer.com/z/3T1W1x1Wd">Godbolt</a>  I prepared earlier using clang gives this error:</p>

<pre data-type="programlisting" data-code-language="bash">&lt;source&gt;:11:10:<code class="w"> </code>error:<code class="w"></code>
no<code class="w"> </code>viable<code class="w"> </code>conversion<code class="w"> </code>from<code class="w"> </code><code class="s1">'istream'</code><code class="w"> </code><code class="o">(</code>aka<code class="w"> </code><code class="s1">'basic_istream&lt;char&gt;'</code><code class="o">)</code><code class="w"> </code>to<code class="w"> </code><code class="s1">'bool'</code><code class="w"></code>
<code class="w">   </code><code class="m">11</code><code class="w"> </code><code class="p">|</code><code class="w">     </code>bool<code class="w"> </code><code class="nv">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code>std::cin<code class="p">;</code><code class="w"></code>
<code class="w">      </code><code class="p">|</code><code class="w">          </code>^<code class="w">    </code>~~~~~~~~<code class="w"></code>
/../../../../include/c++/15.0.0/bits/basic_ios.h:121:16:<code class="w"></code>
note:<code class="w"> </code>explicit<code class="w"> </code>conversion<code class="w"> </code><code class="k">function</code><code class="w"> </code>is<code class="w"> </code>not<code class="w"> </code>a<code class="w"> </code>candidate<code class="w"></code>
<code class="w">  </code><code class="m">121</code><code class="w"> </code><code class="p">|</code><code class="w">       </code>explicit<code class="w"> </code>operator<code class="w"> </code>bool<code class="o">()</code><code class="w"> </code>const<code class="w"></code>
<code class="w">      </code><code class="p">|</code><code class="w">                </code>^<code class="w"></code>
<code class="m">1</code><code class="w"> </code>error<code class="w"> </code>generated.<code class="w"></code>
Compiler<code class="w"> </code>returned:<code class="w"> </code><code class="m">1</code><code class="w"></code></pre>

<p>Other compilers will return a similar error.</p>
<div data-type="tip"><h6>Tip</h6>
<p>Some compiler error messages are long and might seem intimidating. Don’t panic! Just focus on the words you do understand and the line numbers it calls out.</p>
</div>

<p>Operator <code>bool</code> is mentioned in the message, but you also see a <code>const</code> and an <code>explicit</code>.
You saw <code>const</code> earlier in <a data-type="xref" href="#chap_2_variables">“Declaring variables”</a>. Here, it’s used on a stream’s function, meaning it does not change the stream itself.
The keyword <code>explicit</code> means you can only use a function or operator in specific places.
So, using operator <code>bool</code> inside an <code>if</code> statement or another place that needs a <code>bool</code>, called a <em>boolean context</em>, is fine, but you can’t assign the stream to a <code>bool</code>.
The thing to remember is that an <code>if</code> on <code>std::cin</code> is checking that things are OK.</p>

<p>Pulling together what you have learned so far, you now have a small program that attempts to get numeric input and detects problems:</p>
<div id="checked_input_main_code_block" data-type="example">
<h5><span class="label">Example 2-4. </span>Input numbers and check for problems</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO7-1" href="#callout_variables_and_keyboard_input_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Bad!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO7-1" href="#co_variables_and_keyboard_input_CO7-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Treats the stream as a <code>bool</code> to check if it is OK</p></dd>
</dl></div>

<p>This code will extract a number if possible, but leaves unused input behind.
Entering a single number isn’t very interesting, but now you have more C++ under your belt.
You will see how to extend the code to input several numbers in <a data-type="xref" href="ch04.html#chapter_four">Chapter 4</a>.
For the moment, though, let’s improve the code to deal with problems.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="A function for input with some tests"><div class="sect1" id="id59">
<h1>A function for input with some tests</h1>

<p>Putting code in <code>main</code> makes it harder to test. Let’s move the input into its own function, and call that function from <code>main</code>.
You’ll also see a basic way to test code.
Create a new source file called <em>input_with_tests.cpp</em> for this section.</p>

<p>You have only written one function so far, <code>main</code>, but you’ve used several from the standard library.
You know that most functions have a return type, a name, and a set of parentheses to hold parameters sent into the function.
If you write a function to get numeric input, what should its signature be?
You haven’t written any tests yet, and writing tests first can be a good way to think about designing code (this is called Test-Driven Development, or TDD).
C++ doesn’t come with a unit-testing framework, though several external libraries are available.
To keep it simple, you can use the library function <code>assert</code> to check values.</p>








<section data-type="sect2" data-pdf-bookmark="Starting with a failing test"><div class="sect2" id="failing_test">
<h2>Starting with a failing test</h2>

<p>Since you’re putting code in one source file, let’s add a test function there too.
If you’re used to unit testing, you know that you’d normally split out the tests from the code. You can do that with a testing framework, but here, let’s do the simplest thing that works.</p>

<p>Start with an empty function in a file called <em>input_with_tests.cpp</em>, and call it from <code>main</code>.
The test function will call <code>assert</code>, which comes from the <code>&lt;cassert&gt;</code> header.
(The <code>c</code> at the start tells you that this header was originally part of the C standard library.)
Start with this code:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="cp"></code>
<code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="p">}</code><code class="w"></code>

<code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">test_code</code><code class="p">();</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>This doesn’t do much, but it does mean you can now add some tests.</p>

<p>An <code>assert</code> checks a conditional expression, such as <code>0==1</code>, and <em>aborts</em>, or stops, the program if it fails.
The <code>test_code</code> function can therefore be <em>void</em>, since if there’s a problem, it will halt and never return anything.</p>

<p>You’ll start with a failing test. Add a single line to the new <code>test_code</code> function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="mi">0</code><code class="o">==</code><code class="mi">1</code><code class="p">);</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>When you build and run this, you will see an error something like:</p>

<pre data-type="programlisting" data-code-language="bash">./input<code class="w"></code>
input.cpp:53:<code class="w"> </code>void<code class="w"> </code>test_code<code class="o">()</code>:<code class="w"> </code>Assertion<code class="w"> </code><code class="sb">`</code><code class="nv">0</code><code class="o">==</code><code class="m">1</code><code class="err">'</code><code class="w"> </code>failed.<code class="w"></code>
Aborted<code class="w"></code></pre>

<p>The exact message and line number might be different for you, but you can see that an assertion failed.
That’s a good thing.
Now you can delete the <code>assert(0==1)</code> line, because you proved that you’ll get an error if something is wrong.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>The <code>assert</code> macro, a very primitive way of writing a general function, is often only active in debug mode. If you don’t see an error message like “Aborted” or “SIGABRT,” consult the documentation for your compiler flags.</p>
</div>

<p>Now that you have a place to write tests, you can go back to the numeric input itself. I’ll walk you through adding a test and a new function.
Again, you’ll write a failing test first, then make it pass.
I’ll do this slowly, and you might spot possible problems or think of questions as you read.
Bear with me.
By the end of the next section, you will have a small working program to get numeric input, and you’ll know more about writing your own functions.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Breaking your code into functions"><div class="sect2" id="id61">
<h2>Breaking your code into functions</h2>

<p>Your <em>input_with_tests.cpp</em> file now has a basic outline of <code>main</code> and a test function, but it doesn’t do anything much yet.
You’re going to a function that gives a number, so call it <code>get_number</code>.
A function can return <code>void</code> or a specific type and can take zero or more parameters.
Instead of using <code>std::cin</code>, you can use a more general stream type, so your test doesn’t have to wait for user input.
The function needs at least one parameter taking in a stream from which it can extract numbers, but it also needs to report either a number or an error. You can report both in a few ways.</p>

<p>First, you can take a parameter and change it using a reference, indicated by <code>&amp;</code>.
This is called <em>passing by reference</em>.
You met these in the last chapter: a <em>reference</em> is a pointer to something that already exists.
Second, without the ampersand, the parameter is copied. This is called <em>passing by value</em>.
You therefore have two ways to pass parameters: either copy the value or allowing the function to refer to, and possibly change, it:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">a_function</code><code class="p">(</code><code class="kt">int</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO8-1" href="#callout_variables_and_keyboard_input_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="kt">void</code><code class="w"> </code><code class="nf">another_function</code><code class="p">(</code><code class="kt">int</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO8-2" href="#callout_variables_and_keyboard_input_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO8-1" href="#co_variables_and_keyboard_input_CO8-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Passes by value, so the original cannot be changed</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO8-2" href="#co_variables_and_keyboard_input_CO8-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Passes by reference, so the original can be changed</p></dd>
</dl>

<p>Unlike <code>void</code>, <code>get_number</code> can return either the number or error, and take the other one by reference.
<a data-type="xref" href="#fig_2_2">Figure 2-2</a> shows both options.</p>

<figure><div id="fig_2_2" class="figure">
<img src="assets/fig_2_2.png" alt="Return OK and change number or vice versa" width="1280" height="720"/>
<h6><span class="label">Figure 2-2. </span>You can either return OK and change the number, or return some number and change OK.</h6>
</div></figure>

<p>Which way round, though? Let’s think this through.
If you get a number, you can return that. But what number will you return if there is a problem?
If a function promises to return something, it <em>must</em> return something.
You can return any old number plucked out of the air– let’s say42.
If you look back at the function later, though, you might wonder why you chose that value.
Instead, you can do something clearer: return a <code>bool</code> to indicate an error and take the number by reference.</p>

<p>Let’s write a test for a function using this approach.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Starting with a failing test, again"><div class="sect2" id="id62">
<h2>Starting with a failing test, again</h2>

<p>You need a stream and a <code>double</code> to send to the function.
Include the <code>&lt;sstream&gt;</code> header so you can use a type of stream called  <code>std::stringstream</code>.
This is similar to <code>std::cin</code>, but allows you to specify characters up front.
Using a general stream, rather than reaching out to <code>std:cin</code> inside the function, means you can pass in a stream for testing but still use <code>std::cin</code> from <code>main</code>.
If you put a “1” in a stream, using <code>{}</code> to initialize it, you can test to make sure you get a 1 back. If so, everything is OK.</p>

<p>Add this to your existing <code>test_code</code> function. It won’t compile yet, however, because you need to write the <code>get_number</code> function:</p>
<div id="first_get_number_test" data-type="example">
<h5><span class="label">Example 2-5. </span>A simple test of <code>get_number</code> using <code>assert</code> (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;sstream&gt;</code><code class="c1"> </code><a class="co" id="co_variables_and_keyboard_input_CO9-1" href="#callout_variables_and_keyboard_input_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">(</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO9-2" href="#callout_variables_and_keyboard_input_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO9-3" href="#callout_variables_and_keyboard_input_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO9-4" href="#callout_variables_and_keyboard_input_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">OK</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO9-1" href="#co_variables_and_keyboard_input_CO9-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes the string stream header</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO9-2" href="#co_variables_and_keyboard_input_CO9-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Adds testing code</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO9-3" href="#co_variables_and_keyboard_input_CO9-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Uses a string stream instead of <code>std::cin</code></p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO9-4" href="#co_variables_and_keyboard_input_CO9-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Calls a nonexistent function</p></dd>
</dl></div>

<p>Sketching out a test has nudged you towards a function declaration, but the code will not compile until you write the <code>get_number</code> function.
Make the code compile first–then you can make the test pass.</p>

<p>The return is a <code>bool</code>, and you’re sending in some kind of general stream and a numeric type.
You will read the stream and change the number, so these must be references. That way, they’ll allow calling code to see the changed values.
You used a <code>double</code> above, so do the same now, giving this function a signature (or declaration):</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">some_general_stream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">);</code><code class="w"></code></pre>

<p>You know <code>std::cin</code> is an input stream, so what should you use for <code>some_general_stream</code>?
The <code>basic_istream</code> is a template for input streams.
The specific input stream type most suitable for ASCII characters is the <code>std::istream</code>, which will accept <code>std::cin</code> or the <code>std::stringstream</code> you just saw. It lives in the <code>&lt;istream&gt;</code> header, so add that to your includes.</p>
<div data-type="tip"><h6>Tip</h6>
<p>You can include the headers in any order you like, but people often put them in alphabetical order.</p>
</div>

<p>The new function goes in your <em>input_with_tests.cpp</em> file, anywhere outside of <code>main</code>. However, if you call it before the compiler sees a declaration or definition, you’ll get an error.
That’s because the compiler needs to know about the function before you can use it.
You can put the declaration first, then define the function near the end.
For simplicity, write the new function near the top of the file, after your include lines, so the compiler knows about it before you call it in <code>main</code> or <code>test_code</code>.</p>

<p>Start with the signature you discovered during the tests, and add the simplest thing that makes the code compile. The signature says it returns a <code>bool</code>, so return a <code>bool</code>:</p>
<div data-type="example">
<h5><span class="label">Example 2-6. </span>A function to make the code compile, but leaving the test failing.</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="c1">// includes as before
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;istream&gt;</code><code class="c1"> </code><a class="co" id="co_variables_and_keyboard_input_CO10-1" href="#callout_variables_and_keyboard_input_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="cp">
</code><code class="w">
</code><code class="kt">bool</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO10-2" href="#callout_variables_and_keyboard_input_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="nb">false</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="c1">// test_code as before </code><a class="co" id="co_variables_and_keyboard_input_CO10-3" href="#callout_variables_and_keyboard_input_CO10-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="c1">
</code><code class="c1">// main as before</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO10-1" href="#co_variables_and_keyboard_input_CO10-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Includes the istream header</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO10-2" href="#co_variables_and_keyboard_input_CO10-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Defines your new function</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO10-3" href="#co_variables_and_keyboard_input_CO10-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Tests and <code>main</code> as before</p></dd>
</dl></div>

<p>If you build and run this now, your test will fail.
The test expects <code>true</code>, but the function returns <code>false</code>.
If you are used to TDD, you will be familiar with this approach.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Making the test pass"><div class="sect2" id="id63">
<h2>Making the test pass</h2>

<p>The test still fails because your function doesn’t change the number–itjust returns <code>false</code>.
To make the test pass, you must read the number from the stream: <code>input_stream &gt;&gt; number;</code>.
You can then return <code>true</code> if everything is good.</p>

<p>Let’s speed up a little now and add a new test for a failure case after the last test.
Inputting “q” or another letter or two should return <code>false</code>.</p>

<p>Add these extra lines to <code>test_code</code>, starting from annotation <code>1</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">ok</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">unused</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO11-1" href="#callout_variables_and_keyboard_input_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">bad_input</code><code class="p">{</code><code class="s">"</code><code class="s">q</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO11-2" href="#callout_variables_and_keyboard_input_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">not_ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">bad_input</code><code class="p">,</code><code class="w"> </code><code class="n">unused</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO11-3" href="#callout_variables_and_keyboard_input_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="o">!</code><code class="n">not_ok</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO11-4" href="#callout_variables_and_keyboard_input_CO11-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO11-1" href="#co_variables_and_keyboard_input_CO11-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Adds a new test</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO11-2" href="#co_variables_and_keyboard_input_CO11-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Puts something nonnumeric in a stream</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO11-3" href="#co_variables_and_keyboard_input_CO11-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Calls the function</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO11-4" href="#co_variables_and_keyboard_input_CO11-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Checks if things are not OK</p></dd>
</dl>

<p>Now you have two tests, but you still need to add their details to the <code>get_number</code> function.
Use operator <code>&gt;&gt;</code> to get input, and then the stream’s operator <code>bool</code> to check that everything is OK.
If it is, you can return <code>true</code>.
Otherwise, return <code>false</code>.
Add the details to <code>get_number</code>:</p>
<div id="chap_2_get_number" data-type="example">
<h5><span class="label">Example 2-7. </span>Function to get a number from a stream (Work in progress)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">bool</code><code class="w"> </code><code class="nf">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">true</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="w">    </code><code class="k">else</code><code class="w"></code>
<code class="w">    </code><code class="p">{</code><code class="w"></code>
<code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">false</code><code class="p">;</code><code class="w"></code>
<code class="w">    </code><code class="p">}</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre></div>

<p>Build and run your code, and this time your tests should both pass.</p>
<div data-type="tip"><h6>Tip</h6>
<p>So far, you’ve put any <code>if</code> blocks into curly braces. If you only have one line, though, you don’t need to do that. You might therefore see code without the braces:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">if</code><code class="p">(</code><code class="n">is</code><code class="p">)</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="nb">true</code><code class="p">;</code><code class="w"></code>
<code class="k">else</code><code class="w"></code>
<code class="w">    </code><code class="k">return</code><code class="w"> </code><code class="nb">false</code><code class="p">;</code><code class="w"></code></pre>

<p>That’s fine, but if you want to add another statement inside the <code>if</code> or the <code>else</code>, the whitespace might lead you astray. That’s why adding braces is sensible, even if they aren’t required.</p>
</div>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Refactor"><div class="sect2" id="id64">
<h2>Refactor</h2>

<p>Now you have passing tests.
In TDD, you start with a failing test, make it pass, and then you <em>refactor</em>: take a moment to make the code better or tidier.</p>

<p>You can make this function better and learn a little more C++ on the way.
You are capturing the return value in the test, but it’s easy to forget to do this.
Return values are easily ignored. In the next chapter I’ll show you  a more robust approach to reporting problems using exceptions, and another newer C++ feature.</p>

<p>For now, though, you can use a C++17 feature called an <em>attribute</em> in the function head to “encourage” the compiler to generate a warning.
If you add <code>[[nodiscard]]</code> at the start of the function, then discard the return, most compilers will generate a warning.
Add <code>[[nodiscard]]</code> to your function head and rebuild:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[[</code><code class="n">nodiscard</code><code class="p">]]</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="w"></code></pre>

<p>You can temporarily change the function call in <a data-type="xref" href="#first_get_number_test">Example 2-5</a> to ignore the return, and drop the assert:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO12-1" href="#callout_variables_and_keyboard_input_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO12-2" href="#callout_variables_and_keyboard_input_CO12-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO12-1" href="#co_variables_and_keyboard_input_CO12-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Discard the return value</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO12-2" href="#co_variables_and_keyboard_input_CO12-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Only one assert left</p></dd>
</dl>

<p>You will now see a suitable warning:</p>

<pre data-type="programlisting" data-code-language="bash">warning:<code class="w"> </code>ignoring<code class="w"> </code><code class="k">return</code><code class="w"> </code>value<code class="w"> </code>of<code class="w"> </code><code class="s1">'bool get_number(std::istream&amp;, double&amp;)'</code>,<code class="w"></code>
<code class="w"> </code>declared<code class="w"> </code>with<code class="w"> </code>attribute<code class="w"> </code><code class="s1">'nodiscard'</code><code class="w"></code></pre>

<p>The exact message might be different, but you will be warned about ignoring a <code>nodiscard</code> return value.
Useful, right?
Put the <code>test_code</code> back as it was at the beginning of this section, so you don’t discard the return value. Now actually check it:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO13-1" href="#callout_variables_and_keyboard_input_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="n">assert</code><code class="p">(</code><code class="n">ok</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO13-2" href="#callout_variables_and_keyboard_input_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO13-1" href="#co_variables_and_keyboard_input_CO13-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Stores the return value</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO13-2" href="#co_variables_and_keyboard_input_CO13-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Uses the return value</p></dd>
</dl>
</div></section>








<section data-type="sect2" data-pdf-bookmark="Calling your new function from main"><div class="sect2" id="id65">
<h2>Calling your new function from <code>main</code></h2>

<p>You have a function with tests, and you’re ready to use it.
Call the function from <code>main</code>.
Add a message asking for a number just before you call your function, as follows:</p>
<div id="chap_2_main_calling_tests_and_function" data-type="example">
<h5><span class="label">Example 2-8. </span>Calling tests and a function from main</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;cassert&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;istream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;iostream&gt;</code><code class="cp">
</code><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;sstream&gt;</code><code class="cp">
</code><code class="w">
</code><code class="p">[</code><code class="p">[</code><code class="n">nodiscard</code><code class="p">]</code><code class="p">]</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">true</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">false</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">void</code><code class="w"> </code><code class="n">test_code</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">ok</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">unused</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">bad_input</code><code class="p">{</code><code class="s">"</code><code class="s">q</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">not_ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">bad_input</code><code class="p">,</code><code class="w"> </code><code class="n">unused</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="o">!</code><code class="n">not_ok</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="p">}</code><code class="w">
</code><code class="w">
</code><code class="kt">int</code><code class="w"> </code><code class="n">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">test_code</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO14-1" href="#callout_variables_and_keyboard_input_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO14-2" href="#callout_variables_and_keyboard_input_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO14-3" href="#callout_variables_and_keyboard_input_CO14-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">ok</code><code class="p">)</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO14-4" href="#callout_variables_and_keyboard_input_CO14-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">, thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO14-1" href="#co_variables_and_keyboard_input_CO14-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Calls the tests</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO14-2" href="#co_variables_and_keyboard_input_CO14-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Prompts for a number</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO14-3" href="#co_variables_and_keyboard_input_CO14-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Checks for a number in  a function</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO14-4" href="#co_variables_and_keyboard_input_CO14-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Reports the result</p></dd>
</dl></div>

<p>You now have code to elicit input as well as return output, and you’velearned how to detect an error on a stream.</p>

<p>I also noted there might be extra input you hadn’t used, so you should learn how to tidy up if that happens.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Understanding cin and functions in more depth"><div class="sect1" id="id66">
<h1>Understanding cin and functions in more depth</h1>

<p>You are now familiar with <code>std::cin</code>. It’s a type of input stream, or <code>std::istream</code>, and has instance member functions you can call, such as <code>eof</code> and <code>fail</code>.
Along with <code>std::cout</code>, <code>std::cin</code> is a <em>class</em>. You will learn more about classes later in this book.
For now, just know that classes group together functions, and can have <em>member</em> variables that remember values between function calls.
You have seen how to call instance functions using the dot operator, and static functions using the scope resolution operator.</p>

<p>You checked if everything was OK using <code>if(input_stream)</code>, which checks  the state of the stream with <code>fail</code> and <code>bad</code>.
Once a stream returns <code>false</code>, it will continue to return <code>false</code> until you intervene.</p>








<section data-type="sect2" data-pdf-bookmark="Clearing input errors"><div class="sect2" id="chap_2_clearing_input_errors">
<h2>Clearing input errors</h2>

<p>Your code is trying to get a single number.
If there’s a problem, you could have it try again, by adding to the <code>else</code> part in <code>main</code> in <a data-type="xref" href="#chap_2_main_calling_tests_and_function">Example 2-8</a> like this:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">test_code</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">ok</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Got </code><code class="s">"</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">, thanks!</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Something went wrong</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Please enter a number.</code><code class="se">\n</code><code class="s">&gt;</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO15-1" href="#callout_variables_and_keyboard_input_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok_now</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO15-2" href="#callout_variables_and_keyboard_input_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="k">if</code><code class="p">(</code><code class="o">!</code><code class="n">ok_now</code><code class="p">)</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO15-3" href="#callout_variables_and_keyboard_input_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">{</code><code class="w">
</code><code class="w">            </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="s">"</code><code class="s">Something went wrong again</code><code class="se">\n</code><code class="s">"</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO15-4" href="#callout_variables_and_keyboard_input_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO15-1" href="#co_variables_and_keyboard_input_CO15-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Prompts for a second attempt</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO15-2" href="#co_variables_and_keyboard_input_CO15-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Tries to get a number again</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO15-3" href="#co_variables_and_keyboard_input_CO15-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Checks the stream’s state</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO15-4" href="#co_variables_and_keyboard_input_CO15-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Reports if there is another problem</p></dd>
</dl>

<p>Try this by typing something nonnumeric, for example a <em>q</em>, and then follow that by entering a genuine number on the next attempt.
The <code>get_number</code> function will continue to return <code>false</code>:</p>

<pre data-type="programlisting" data-code-language="bash"><code>Please</code><code class="w"> </code><code>enter</code><code class="w"> </code><code>a</code><code class="w"> </code><code>number.</code><code class="w">
</code><code>&gt;q</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO16-1" href="#callout_variables_and_keyboard_input_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code>Something</code><code class="w"> </code><code>went</code><code class="w"> </code><code>wrong</code><code class="w">
</code><code>Please</code><code class="w"> </code><code>enter</code><code class="w"> </code><code>a</code><code class="w"> </code><code>number.</code><code class="w">
</code><code>&gt;1</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO16-2" href="#callout_variables_and_keyboard_input_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code>Something</code><code class="w"> </code><code>went</code><code class="w"> </code><code>wrong</code><code class="w"> </code><code>again</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO16-3" href="#callout_variables_and_keyboard_input_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO16-1" href="#co_variables_and_keyboard_input_CO16-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Enters something invalid</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO16-2" href="#co_variables_and_keyboard_input_CO16-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Tries to enter something valid</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO16-3" href="#co_variables_and_keyboard_input_CO16-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Shows an error, even for a number</p></dd>
</dl>

<p>Even though you entered a number on the second attempt, the code still reports a problem.</p>

<p>You can fix this by resetting the stream’s state using the <code>clear</code> function:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">();</code><code class="w"></code></pre>

<p>When you call the <code>clear</code> function, the stream will report <code>true</code> next time you ask what state it’s in.
In fact, you <em>must</em> call <code>clear</code> in order to use the stream again.
(This function can do more–https://en.cppreference.com/w/cpp/io/basic_ios/clear[CppReference] gives more details if you’re interested.)</p>

<p>Now, whatever caused the problem is still in the stream–for example, that <em>q</em> you typed.
You can tidy that up, too, using the stream’s <code>ignore</code> function. You tell the <code>ignore</code> function how many characters to clear up at most, along with a delimiting character to stop at, such as <code>\n</code>.
You need to <code>clear</code> the stream first, then <code>ignore</code> some characters up to a newline character:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO17-1" href="#callout_variables_and_keyboard_input_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO17-2" href="#callout_variables_and_keyboard_input_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">some_length</code><code class="p">,</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO17-3" href="#callout_variables_and_keyboard_input_CO17-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO17-4" href="#callout_variables_and_keyboard_input_CO17-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">  </code><code class="p">)</code><code class="p">;</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO17-1" href="#co_variables_and_keyboard_input_CO17-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Clears the error so everything is OK again</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO17-2" href="#co_variables_and_keyboard_input_CO17-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Ignores leftover characters</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO17-3" href="#co_variables_and_keyboard_input_CO17-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Until it gets up to some specified length</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO17-4" href="#co_variables_and_keyboard_input_CO17-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Or until it gets to a newline character</p></dd>
</dl>

<p>Before you add this to your function in <a data-type="xref" href="#chap_2_get_number_and_clear_problems">Example 2-9</a>, you need to decide what <code>some_length</code> should be.</p>

<p>Now, since you don’t know how much is left in the stream after a read, most people tend to pick the maximum possible size when they call <code>ignore</code>.
What is a stream’s maximum possible size?
Well, a stream’s size is a mysterious type called <code>std::streamsize</code>.  You can find out the maximum <code>std::streamsize</code> by calling <code>numeric_limits</code>’s <code>max</code> function, which you met earlier.</p>

<p>This time, you’ll ask for the <code>streamsize</code> version using angle brackets: <code>std::numeric_limits&lt;std::streamsize&gt;</code>.
As before, the angle brackets <code>&lt;&gt;</code> mean you are using a template. Next you’ll use use the familiar scope resolution operator <code>::</code>, followed by the function, <code>max</code>, to get the value for <code>some_length</code>:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">std</code><code class="o">::</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">::</code><code class="n">streamsize</code><code class="o">&gt;::</code><code class="n">max</code><code class="p">()</code><code class="w"></code></pre>

<p>Adding calls to both <code>clear</code> and <code>ignore</code> in <code>get_number</code> will improve  problem handling, so do that now.
Don’t forget to include the <code>&lt;limits&gt;</code> header to find the <code>max</code> size:</p>
<div id="chap_2_get_number_and_clear_problems" data-type="example">
<h5><span class="label">Example 2-9. </span>An even better function to get a number from a stream, while handling problems</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="cp">#</code><code class="cp">include</code><code class="w"> </code><code class="cpf">&lt;limits&gt;</code><code class="cp">
</code><code class="w">
</code><code class="p">[</code><code class="p">[</code><code class="n">nodiscard</code><code class="p">]</code><code class="p">]</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-1" href="#callout_variables_and_keyboard_input_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">input_stream</code><code class="p">,</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-2" href="#callout_variables_and_keyboard_input_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">number</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-3" href="#callout_variables_and_keyboard_input_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">input_stream</code><code class="w"> </code><code class="o">&gt;</code><code class="o">&gt;</code><code class="w"> </code><code class="n">number</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="k">if</code><code class="p">(</code><code class="n">input_stream</code><code class="p">)</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">true</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="w">    </code><code class="k">else</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">clear</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-4" href="#callout_variables_and_keyboard_input_CO18-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">input_stream</code><code class="p">.</code><code class="n">ignore</code><code class="p">(</code><code class="w">  </code><a class="co" id="co_variables_and_keyboard_input_CO18-5" href="#callout_variables_and_keyboard_input_CO18-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">                    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">numeric_limits</code><code class="o">&lt;</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">streamsize</code><code class="o">&gt;</code><code class="o">:</code><code class="o">:</code><code class="n">max</code><code class="p">(</code><code class="p">)</code><code class="p">,</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-6" href="#callout_variables_and_keyboard_input_CO18-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">                    </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO18-7" href="#callout_variables_and_keyboard_input_CO18-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="w">                 </code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">
</code><code class="w">        </code><code class="k">return</code><code class="w"> </code><code class="nb">false</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-1" href="#co_variables_and_keyboard_input_CO18-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Provokes a warning if the return is discarded</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-2" href="#co_variables_and_keyboard_input_CO18-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Passes parameters by reference so they can be changed</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-3" href="#co_variables_and_keyboard_input_CO18-4"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Clears the failed flag</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-4" href="#co_variables_and_keyboard_input_CO18-5"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Ignores leftover input</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-5" href="#co_variables_and_keyboard_input_CO18-6"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Until it reaches the maximum possible number of characters</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO18-6" href="#co_variables_and_keyboard_input_CO18-7"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Or until it gets to a newline character</p></dd>
</dl></div>

<p>You now have a better function to get numeric input. If you left the extra code to call <code>get_number</code> again after a failure, you can now enter a <em>q</em> and then a number. This time,
you won’t see “Something went wrong again.”
Here’s a <a href="https://godbolt.org/z/oMT9oxPqz">Godbolt</a> if you need it.</p>
</div></section>








<section data-type="sect2" data-pdf-bookmark="More on Functions"><div class="sect2" id="id68">
<h2>More on Functions</h2>

<p>You’ve written a couple of functions besides <code>main</code>.
Let’s try some further experiments now, to ensure that you understand functions in a bit more detail.
I’ll suggest changes to make so you can see their effects.
You’re going to get warnings and errors and your tests will sometimes fail, but you can put your code back afterwards.
The best way to learn is often by breaking things.
Getting familiar with warnings and errors will also help you learn how to fix things in the future.</p>

<p>Write a new function in your <em>input_with_tests.cpp</em> file, somewhere above <code>main</code>, and call it from <code>main</code>.
The <code>get_number</code> function takes a <code>double</code>.
What happens if you change <code>number</code> to an <code>int</code>?
Try changing the <code>number</code>’s type as follows and build your code:</p>
<div id="further_experiments_wrong_type" data-type="example">
<h5><span class="label">Example 2-10. </span>Sending the wrong type to a function (will not compile)</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">some_experiments</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="kt">int</code><code class="w"> </code><code class="n">number</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO19-1" href="#callout_variables_and_keyboard_input_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cin</code><code class="p">,</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">cout</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">&lt;</code><code class="o">&lt;</code><code class="w"> </code><code class="sc">'</code><code class="sc">\n</code><code class="sc">'</code><code class="p">;</code><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO19-1" href="#co_variables_and_keyboard_input_CO19-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Change <code>number</code> to an <code>int</code></p></dd>
</dl></div>

<p>You’ll get an <em>error</em> when you try to compile this code.
The exact wording varies between compilers, but you’ll see something like one of the following:</p>

<pre data-type="programlisting" data-code-language="bash">error:<code class="w"> </code>cannot<code class="w"> </code><code class="nb">bind</code><code class="w"> </code>non-const<code class="w"> </code>lvalue<code class="w"> </code>reference<code class="w"> </code>of<code class="w"> </code><code class="nb">type</code><code class="w"> </code><code class="s1">'double&amp;'</code><code class="w"></code>
<code class="w"> </code>to<code class="w"> </code>a<code class="w"> </code>value<code class="w"> </code>of<code class="w"> </code><code class="nb">type</code><code class="w"> </code><code class="s1">'int'</code><code class="w"></code></pre>

<p>or</p>

<pre data-type="programlisting" data-code-language="bash">error:<code class="w"> </code>no<code class="w"> </code>matching<code class="w"> </code><code class="k">function</code><code class="w"> </code><code class="k">for</code><code class="w"> </code>call<code class="w"> </code>to<code class="w"> </code><code class="s1">'get_number'</code><code class="w"></code>
note:<code class="w"> </code>candidate<code class="w"> </code><code class="k">function</code><code class="w"> </code>not<code class="w"> </code>viable:<code class="w"> </code>no<code class="w"> </code>known<code class="w"> </code>conversion<code class="w"> </code>from<code class="w"></code>
<code class="w"> </code><code class="s1">'int'</code><code class="w"> </code>to<code class="w"> </code><code class="s1">'double &amp;'</code><code class="w"> </code><code class="k">for</code><code class="w"> </code>2nd<code class="w"> </code>argument<code class="w"></code></pre>

<p>or</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="n">error</code><code class="w"> </code><code class="n">C2664</code><code class="o">:</code><code class="w"> </code><code class="err">'</code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="p">,</code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="p">)</code><code class="err">'</code><code class="o">:</code><code class="w"></code>
<code class="w"> </code><code class="n">cannot</code><code class="w"> </code><code class="n">convert</code><code class="w"> </code><code class="n">argument</code><code class="w"> </code><code class="mi">2</code><code class="w"> </code><code class="n">from</code><code class="w"> </code><code class="err">'</code><code class="kt">int</code><code class="err">'</code><code class="w"> </code><code class="n">to</code><code class="w"> </code><code class="err">'</code><code class="kt">double</code><code class="w"> </code><code class="o">&amp;</code><code class="err">'</code><code class="w"></code>
<code class="n">input</code><code class="p">.</code><code class="n">cpp</code><code class="p">(</code><code class="mi">55</code><code class="p">)</code><code class="o">:</code><code class="w"> </code><code class="n">note</code><code class="o">:</code><code class="w"> </code><code class="n">see</code><code class="w"> </code><code class="n">declaration</code><code class="w"> </code><code class="n">of</code><code class="w"> </code><code class="err">'</code><code class="n">get_number</code><code class="err">'</code><code class="w"></code>
<code class="n">input</code><code class="p">.</code><code class="n">cpp</code><code class="p">(</code><code class="mi">89</code><code class="p">)</code><code class="o">:</code><code class="w"> </code><code class="n">note</code><code class="o">:</code><code class="w"> </code><code class="k">while</code><code class="w"> </code><code class="n">trying</code><code class="w"> </code><code class="n">to</code><code class="w"> </code><code class="n">match</code><code class="w"> </code><code class="n">the</code><code class="w"> </code><code class="n">argument</code><code class="w"> </code><code class="n">list</code><code class="w"></code>
<code class="w"> </code><code class="err">'</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="p">,</code><code class="w"> </code><code class="kt">int</code><code class="p">)</code><code class="err">'</code><code class="w"></code></pre>

<p>The compiler sees a function that expects a reference to a <code>double</code>, so it complains when you send in an <code>int</code>.
This is a good thing.
C++ is a <em>strongly typed</em> language, and you will use types to help you write better code over the course of this book.
If a function expects a <em>double</em>, you must give it a <em>double</em>.
You can use the type system to find errors at compile time, which is another reason C++ is powerful.</p>

<p>Change <code>number</code> back to a <code>double</code> so the code will compile again.
Then let’s try one more experiment with this function.</p>

<p>The parameters to <code>get_number</code> are passed by reference, using the <code>&amp;</code> symbol.
Drop the <code>&amp;</code> symbol for the second parameter:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="p">[[</code><code class="n">nodiscard</code><code class="p">]]</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">std</code><code class="o">::</code><code class="n">istream</code><code class="w"> </code><code class="o">&amp;</code><code class="w"> </code><code class="n">is</code><code class="p">,</code><code class="w"> </code><code class="kt">double</code><code class="w"> </code><code class="n">number</code><code class="p">)</code><code class="w"></code></pre>

<p>Now build your code again, and you won’t see any errors or warnings.
When you run it, your tests will fail.
Using <code>assert</code> isn’t as helpful as a proper testing framework, so you’ll only see a message similar to this one:</p>

<pre data-type="programlisting" data-code-language="bash">Assertion<code class="w"> </code><code class="s1">'value == 1'</code><code class="w"> </code>failed<code class="w"></code></pre>

<p>The test code declares a number <code>double value{};</code>, which starts with a value of zero.
Without the reference symbol &amp;, the <code>get_number</code> function takes the double by value, so it only sees a copy of the original.
Any changes happen to the copy in the function, not to the original value, which remains 0.
The assert is therefore checking if <code>value</code> (which is 0) equals 1, so it fails.
Now put the <code>&amp;</code> back and rebuild to check that everything is OK again.</p>

<p>Let’s think about functions a bit more.
You wrote some tests and checking that everything worked:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">stringstream</code><code class="w"> </code><code class="n">some_input</code><code class="p">{</code><code class="s">"1"</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">some_input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">ok</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">==</code><code class="w"> </code><code class="mi">1</code><code class="p">);</code><code class="w"></code>

<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">unused</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">stringstream</code><code class="w"> </code><code class="n">bad_input</code><code class="p">{</code><code class="s">"q"</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">not_ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">bad_input</code><code class="p">,</code><code class="w"> </code><code class="n">unused</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="o">!</code><code class="n">not_ok</code><code class="p">);</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>You cannot see <code>value</code> or any of the other variables from outside the function.
If you try to use the function’s variable <code>value</code> from <code>main</code>, you’ll get an error.
Try it:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">int</code><code class="w"> </code><code class="nf">main</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="n">test_code</code><code class="p">(</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO20-1" href="#callout_variables_and_keyboard_input_CO20-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO20-2" href="#callout_variables_and_keyboard_input_CO20-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">    </code><code class="c1">// rest of code as before
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO20-1" href="#co_variables_and_keyboard_input_CO20-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Calls test_code where <code>value</code> lives</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO20-2" href="#co_variables_and_keyboard_input_CO20-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Tries to use <code>value</code></p></dd>
</dl>

<p>You get an error citing <code>use of undeclared identifier <em>value</em></code>.
A function’s variables “live” for the <em>scope</em> of the function only, and you can’t reach inside a function to use its <em>local</em> variables.
That might be familiar if you’re used to other programming languages.
Now, remove the <code>assert</code> line that broke your code.</p>

<p>Let’s try something else using scopes.
You can put blocks of code inside curly braces in a function to break it into chunks. This is called <em>block scope</em>.  Why is it useful? Well, try to use the same variable names for both tests.
Either think through what might happen or try it:</p>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">()</code><code class="w"></code>
<code class="p">{</code><code class="w"></code>
<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">stringstream</code><code class="w"> </code><code class="n">input</code><code class="p">{</code><code class="s">"1"</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">ok</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">==</code><code class="w"> </code><code class="mi">1</code><code class="p">);</code><code class="w"></code>

<code class="w">    </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{};</code><code class="w"></code>
<code class="w">    </code><code class="n">std</code><code class="o">::</code><code class="n">stringstream</code><code class="w"> </code><code class="n">input</code><code class="p">{</code><code class="s">"q"</code><code class="p">};</code><code class="w"></code>
<code class="w">    </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">ok</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">);</code><code class="w"></code>
<code class="w">    </code><code class="n">assert</code><code class="p">(</code><code class="o">!</code><code class="n">ok</code><code class="p">);</code><code class="w"></code>
<code class="p">}</code><code class="w"></code></pre>

<p>If you try to build the code now, you’ll get errors complaining about a <code>redefinition of <em>value</em>, <em>input</em> and <em>OK</em></code>.
If you put each test into a separate block, though, the variables only live to the end of the block–that is, until the closing <code>}</code>.
The following version will therefore compile:</p>
<div id="chap_2_test_blocks" data-type="example">
<h5><span class="label">Example 2-11. </span>Tests in smaller block scope</h5>

<pre data-type="programlisting" data-code-language="cpp"><code class="kt">void</code><code class="w"> </code><code class="nf">test_code</code><code class="p">(</code><code class="p">)</code><code class="w">
</code><code class="p">{</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-1" href="#callout_variables_and_keyboard_input_CO21-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-2" href="#callout_variables_and_keyboard_input_CO21-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">input</code><code class="p">{</code><code class="s">"</code><code class="s">1</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">OK</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="n">value</code><code class="w"> </code><code class="o">=</code><code class="o">=</code><code class="w"> </code><code class="mi">1</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-3" href="#callout_variables_and_keyboard_input_CO21-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a><code class="w">
</code><code class="w">
</code><code class="w">    </code><code class="p">{</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-4" href="#callout_variables_and_keyboard_input_CO21-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="kt">double</code><code class="w"> </code><code class="n">value</code><code class="p">{</code><code class="p">}</code><code class="p">;</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-5" href="#callout_variables_and_keyboard_input_CO21-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a><code class="w">
</code><code class="w">        </code><code class="n">std</code><code class="o">:</code><code class="o">:</code><code class="n">stringstream</code><code class="w"> </code><code class="n">input</code><code class="p">{</code><code class="s">"</code><code class="s">q</code><code class="s">"</code><code class="p">}</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="k">const</code><code class="w"> </code><code class="kt">bool</code><code class="w"> </code><code class="n">OK</code><code class="w"> </code><code class="o">=</code><code class="w"> </code><code class="n">get_number</code><code class="p">(</code><code class="n">input</code><code class="p">,</code><code class="w"> </code><code class="n">value</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">        </code><code class="n">assert</code><code class="p">(</code><code class="o">!</code><code class="n">OK</code><code class="p">)</code><code class="p">;</code><code class="w">
</code><code class="w">    </code><code class="p">}</code><code class="w"> </code><a class="co" id="co_variables_and_keyboard_input_CO21-6" href="#callout_variables_and_keyboard_input_CO21-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a><code class="w">
</code><code class="p">}</code></pre>
<dl class="calloutlist">
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-1" href="#co_variables_and_keyboard_input_CO21-1"><img src="assets/1.png" alt="1" width="12" height="12"/></a></dt>
<dd><p>Opens a new scope</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-2" href="#co_variables_and_keyboard_input_CO21-2"><img src="assets/2.png" alt="2" width="12" height="12"/></a></dt>
<dd><p>Declares a variable called <code>value</code></p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-3" href="#co_variables_and_keyboard_input_CO21-3"><img src="assets/3.png" alt="3" width="12" height="12"/></a></dt>
<dd><p>Closes the scope, so <code>value</code> is <em>out of scope</em></p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-4" href="#co_variables_and_keyboard_input_CO21-4"><img src="assets/4.png" alt="4" width="12" height="12"/></a></dt>
<dd><p>Opens another new scope</p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-5" href="#co_variables_and_keyboard_input_CO21-5"><img src="assets/5.png" alt="5" width="12" height="12"/></a></dt>
<dd><p>Declares a new variable called <code>value</code></p></dd>
<dt><a class="co" id="callout_variables_and_keyboard_input_CO21-6" href="#co_variables_and_keyboard_input_CO21-6"><img src="assets/6.png" alt="6" width="12" height="12"/></a></dt>
<dd><p>Closes the second scope, so the second <code>value</code> is also <em>out of scope</em></p></dd>
</dl></div>

<p>Splitting the two blocks into separate functions is another option, but this demonstrates a bit more about scope.</p>
<div data-type="warning" epub:type="warning"><h6>Warning</h6>
<p>Knowing that variables are only visible inside a scope is important.
However, if you find you have separate blocks inside a function. this might indicate that you should split your function into smaller, named functions.</p>
</div>

<p>When a variable goes out of scope, it no longer exists, which frees up memory.
Some languages, like Java and Python, use a <em>garbage collector</em> to tidy up, so you can’t be sure when this will happen.
In contrast, C++ gives you precise control, which can help you keep your programs smaller and faster.
Scope will crop up again over the rest of the book.</p>
</div></section>
</div></section>






<section data-type="sect1" data-pdf-bookmark="Conclusion"><div class="sect1" id="id69">
<h1>Conclusion</h1>

<p>You have seen lots of new C++ in this chapter.
Though the main aim was learning to get input, you had to declare variables, and you practiced writing functions and used the stream extraction operator <code>&gt;&gt;</code> to get input for an <code>int</code> and a <code>double</code> in the process.</p>

<p>You needed a variable to hold input, so you learned about declaring and initializing variables:</p>

<ul>
<li>
<p>Variables have a type, like <code>int</code> or <code>double</code>. They might not be initialized, which can be problematic</p>
</li>
<li>
<p>You can use a single equals sign, <code>=</code>, to assign an initial value. You can also use empty braces <code>{}</code>, or even braces with a value if you want something other than the default.</p>
</li>
<li>
<p>You can declare a “variable” as <code>const</code>, meaning you can’t change its value later.</p>
</li>
</ul>

<p>You now know the basics of declaring variables.
You wrote a test and input function. These two functions introduced some important ideas:</p>

<ul>
<li>
<p>Taking parameters by reference, using <code>&amp;</code>, allows you to change their values. This is called passing by reference.</p>
</li>
<li>
<p>Without a <code>&amp;</code>, the parameters are copied and only changed locally in a function. This is called passing by value.</p>
</li>
<li>
<p>The <code>[[nodiscard]]</code> attribute provokes a warning if the return value is ignored.</p>
</li>
<li>
<p>You must send parameters that match the expected types; otherwise, you’ll get an error.</p>
</li>
</ul>

<p>You have used several keywords, parts of the core language, and library functions too. <a data-type="xref" href="#table_2_1">Table 2-1</a> lists what you have met so far.</p>
<table id="table_2_1">
<caption><span class="label">Table 2-1. </span>Table of C++ fundamental types, operators, and library functions</caption>
<thead>
<tr>
<th>Term</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><p>int</p></td>
<td><p>Whole numbers, like 42</p></td>
</tr>
<tr>
<td><p>double</p></td>
<td><p>Floating point numbers like 42.1</p></td>
</tr>
<tr>
<td><p>=</p></td>
<td><p>assigns a value</p></td>
</tr>
<tr>
<td><p><code>!</code></p></td>
<td><p>not</p></td>
</tr>
<tr>
<td><p>&amp;&amp;</p></td>
<td><p>and</p></td>
</tr>
<tr>
<td><p><code>==</code></p></td>
<td><p>equals</p></td>
</tr>
<tr>
<td><p><code>!=</code></p></td>
<td><p>not equals</p></td>
</tr>
<tr>
<td><p>operator&gt;&gt;</p></td>
<td><p>stream extraction operator, defined for various input streams</p></td>
</tr>
<tr>
<td><p>std::istream</p></td>
<td><p>a general input stream</p></td>
</tr>
<tr>
<td><p>std::cin</p></td>
<td><p>global object giving input, often from a keyboard</p></td>
</tr>
<tr>
<td><p>std::stringstream</p></td>
<td><p>a stream for strings, providing insertion and extraction</p></td>
</tr>
<tr>
<td><p>assert</p></td>
<td><p>checks an expression in brackets and aborts the program if it is false</p></td>
</tr>
</tbody>
</table>

<p>Templates and classes got a brief mention, and will make appearances throughout the rest of this book.
You saw how to call class instance functions using the dot operator, and static functions using the scope resolution operator.
You also caused a few compiler warnings, at my encouragement.  Warnings are useful, but they don’t stop the code from compiling.
They might suggest that you’ve forgotten something, and you’ll see many more of them as you learn C++. Pay attention to them.</p>

<p>You started to think about error handling, returning a <code>bool</code> to indicate success or failure.
In the next chapter you will learn alternative ways to indicate problems, including exception handling.</p>
</div></section>
</div></section></div></div></body></html>