module github.com/yourusername/epubcraft

go 1.24.4

require (
	github.com/bmaupin/go-epub v1.1.0
	github.com/briandowns/spinner v1.23.2
	github.com/fatih/color v1.7.0
	github.com/mattn/go-runewidth v0.0.16
	github.com/spf13/cobra v1.9.1
	github.com/taylorskalyo/goreader v1.0.1
	github.com/unidoc/unipdf/v3 v3.69.0
	golang.org/x/net v0.35.0
	golang.org/x/term v0.32.0
)

require (
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/gofrs/uuid v3.1.0+incompatible // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/mattn/go-colorable v0.1.2 // indirect
	github.com/mattn/go-isatty v0.0.8 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	github.com/unidoc/freetype v0.2.3 // indirect
	github.com/unidoc/pkcs7 v0.2.0 // indirect
	github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a // indirect
	github.com/unidoc/unitype v0.5.1 // indirect
	github.com/vincent-petithory/dataurl v1.0.0 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/image v0.24.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/xerrors v0.0.0-20240903120638-7835f813f4da // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
